"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/demo/page",{

/***/ "(app-pages-browser)/./app/editor/components/Toolbar/EditorToolbar.tsx":
/*!*********************************************************!*\
  !*** ./app/editor/components/Toolbar/EditorToolbar.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditorToolbar: function() { return /* binding */ EditorToolbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/color-mode/color-mode-context.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/tooltip/tooltip.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/icon-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/button-group.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu-list.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu-item.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/ArrowBack.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Repeat.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/View.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Settings.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Download.mjs\");\n/* harmony import */ var _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stores/editorStore */ \"(app-pages-browser)/./lib/stores/editorStore.ts\");\n/* __next_internal_client_entry_do_not_use__ EditorToolbar auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction EditorToolbar(param) {\n    let { onPreview } = param;\n    _s();\n    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"gray.200\", \"gray.700\");\n    const bgColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"white\", \"gray.800\");\n    const { currentPage, currentBreakpoint, setBreakpoint, undo, redo, undoStack, redoStack } = (0,_lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_1__.useEditorStore)();\n    const breakpointIcons = {\n        desktop: \"\\uD83D\\uDDA5️\",\n        tablet: \"\\uD83D\\uDCF1\",\n        mobile: \"\\uD83D\\uDCF1\"\n    };\n    const handleSave = ()=>{\n        // TODO: Implement save functionality\n        console.log(\"Saving page...\", currentPage);\n    };\n    const handlePublish = ()=>{\n        // TODO: Implement publish functionality\n        console.log(\"Publishing page...\", currentPage);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        h: \"60px\",\n        bg: bgColor,\n        borderBottom: \"1px\",\n        borderColor: borderColor,\n        px: 4,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n            align: \"center\",\n            justify: \"space-between\",\n            h: \"100%\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                    align: \"center\",\n                    gap: 3,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                            label: \"Back to Dashboard\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                \"aria-label\": \"Back\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__.ArrowBackIcon, {}, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 21\n                                }, void 0),\n                                variant: \"ghost\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Divider, {\n                            orientation: \"vertical\",\n                            h: \"24px\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                            fontSize: \"sm\",\n                            fontWeight: \"medium\",\n                            color: \"gray.700\",\n                            children: (currentPage === null || currentPage === void 0 ? void 0 : currentPage.name) || \"Untitled Page\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                    align: \"center\",\n                    gap: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                            fontSize: \"xs\",\n                            color: \"gray.500\",\n                            mr: 2,\n                            children: \"Device:\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ButtonGroup, {\n                            size: \"sm\",\n                            isAttached: true,\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDDA5️\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: currentBreakpoint === \"desktop\" ? \"blue\" : \"gray\",\n                                    onClick: ()=>setBreakpoint(\"desktop\"),\n                                    fontSize: \"xs\",\n                                    children: \"Desktop\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: currentBreakpoint === \"tablet\" ? \"blue\" : \"gray\",\n                                    onClick: ()=>setBreakpoint(\"tablet\"),\n                                    fontSize: \"xs\",\n                                    children: \"Tablet\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: currentBreakpoint === \"mobile\" ? \"blue\" : \"gray\",\n                                    onClick: ()=>setBreakpoint(\"mobile\"),\n                                    fontSize: \"xs\",\n                                    children: \"Mobile\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                    align: \"center\",\n                    gap: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ButtonGroup, {\n                            size: \"sm\",\n                            variant: \"ghost\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                    label: \"Undo\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                        \"aria-label\": \"Undo\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__.ArrowBackIcon, {\n                                            transform: \"scaleX(-1)\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: undo,\n                                        isDisabled: undoStack.length === 0\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                    label: \"Redo\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                        \"aria-label\": \"Redo\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_12__.RepeatIcon, {}, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: redo,\n                                        isDisabled: redoStack.length === 0\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Divider, {\n                            orientation: \"vertical\",\n                            h: \"24px\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                            label: \"Preview\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                \"aria-label\": \"Preview\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__.ViewIcon, {}, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 21\n                                }, void 0),\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onPreview\n                            }, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Menu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.MenuButton, {\n                                    as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton,\n                                    \"aria-label\": \"Settings\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_16__.SettingsIcon, {}, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    variant: \"ghost\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.MenuList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.MenuItem, {\n                                            onClick: handleSave,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_19__.DownloadIcon, {\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Save Page\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.MenuItem, {\n                                            onClick: handlePublish,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__.ViewIcon, {\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Publish Page\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Divider, {\n                            orientation: \"vertical\",\n                            h: \"24px\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ButtonGroup, {\n                            size: \"sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleSave,\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    colorScheme: \"blue\",\n                                    onClick: handlePublish,\n                                    children: \"Publish\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorToolbar, \"MeYd6vzhnuAaY/PBWNQPZitJLII=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue,\n        _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_1__.useEditorStore\n    ];\n});\n_c = EditorToolbar;\nvar _c;\n$RefreshReg$(_c, \"EditorToolbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/editor/components/Toolbar/EditorToolbar.tsx\n"));

/***/ })

});