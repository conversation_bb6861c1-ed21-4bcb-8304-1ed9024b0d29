"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/demo/page",{

/***/ "(app-pages-browser)/./app/editor/page.tsx":
/*!*****************************!*\
  !*** ./app/editor/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/color-mode/color-mode-context.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _components_Canvas_DragDropCanvas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Canvas/DragDropCanvas */ \"(app-pages-browser)/./app/editor/components/Canvas/DragDropCanvas.tsx\");\n/* harmony import */ var _components_Palette_ComponentPalette__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/Palette/ComponentPalette */ \"(app-pages-browser)/./app/editor/components/Palette/ComponentPalette.tsx\");\n/* harmony import */ var _components_Properties_PropertyPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Properties/PropertyPanel */ \"(app-pages-browser)/./app/editor/components/Properties/PropertyPanel.tsx\");\n/* harmony import */ var _components_Layers_LayersPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/Layers/LayersPanel */ \"(app-pages-browser)/./app/editor/components/Layers/LayersPanel.tsx\");\n/* harmony import */ var _components_Toolbar_EditorToolbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/Toolbar/EditorToolbar */ \"(app-pages-browser)/./app/editor/components/Toolbar/EditorToolbar.tsx\");\n/* harmony import */ var _components_Preview_ResponsivePreview__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/Preview/ResponsivePreview */ \"(app-pages-browser)/./app/editor/components/Preview/ResponsivePreview.tsx\");\n/* harmony import */ var _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/stores/editorStore */ \"(app-pages-browser)/./lib/stores/editorStore.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction EditorPage() {\n    _s();\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const bgColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.useColorModeValue)(\"gray.50\", \"gray.900\");\n    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.useColorModeValue)(\"gray.200\", \"gray.700\");\n    const { currentPage, selectedElement, isPreviewMode, currentBreakpoint } = (0,_lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_8__.useEditorStore)();\n    if (showPreview) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Preview_ResponsivePreview__WEBPACK_IMPORTED_MODULE_7__.ResponsivePreview, {\n            onClose: ()=>setShowPreview(false)\n        }, void 0, false, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n            lineNumber: 32,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Box, {\n        h: \"100vh\",\n        bg: bgColor,\n        overflow: \"hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toolbar_EditorToolbar__WEBPACK_IMPORTED_MODULE_6__.EditorToolbar, {\n                onPreview: ()=>setShowPreview(true)\n            }, void 0, false, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Flex, {\n                h: \"calc(100vh - 60px)\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Box, {\n                        w: \"280px\",\n                        borderRight: \"1px\",\n                        borderColor: borderColor,\n                        bg: \"white\",\n                        overflowY: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Palette_ComponentPalette__WEBPACK_IMPORTED_MODULE_3__.ComponentPalette, {}, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Flex, {\n                        flex: \"1\",\n                        direction: \"column\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Canvas_DragDropCanvas__WEBPACK_IMPORTED_MODULE_2__.DragDropCanvas, {}, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Box, {\n                        w: \"320px\",\n                        borderLeft: \"1px\",\n                        borderColor: borderColor,\n                        bg: \"white\",\n                        overflowY: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Flex, {\n                            direction: \"column\",\n                            h: \"100%\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Box, {\n                                    flex: \"1\",\n                                    borderBottom: \"1px\",\n                                    borderColor: borderColor,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layers_LayersPanel__WEBPACK_IMPORTED_MODULE_5__.LayersPanel, {}, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.Box, {\n                                    flex: \"1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Properties_PropertyPanel__WEBPACK_IMPORTED_MODULE_4__.PropertyPanel, {}, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorPage, \"+G9mp+gR+G9INUKSTYQe2Dw3EsA=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.useColorModeValue,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.useColorModeValue,\n        _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_8__.useEditorStore\n    ];\n});\n_c = EditorPage;\nvar _c;\n$RefreshReg$(_c, \"EditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/editor/page.tsx\n"));

/***/ })

});