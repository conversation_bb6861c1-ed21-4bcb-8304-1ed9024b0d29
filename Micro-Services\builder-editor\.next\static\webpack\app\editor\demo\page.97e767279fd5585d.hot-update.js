"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/demo/page",{

/***/ "(app-pages-browser)/./lib/stores/editorStore.ts":
/*!***********************************!*\
  !*** ./lib/stores/editorStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditorStore: function() { return /* binding */ useEditorStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/../../node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/../../node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useEditorStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((set, get)=>({\n        // Initial state\n        currentPage: null,\n        selectedElement: null,\n        selectedSection: null,\n        selectedElements: [],\n        clipboard: {\n            elements: [],\n            sections: []\n        },\n        isPreviewMode: false,\n        currentBreakpoint: \"desktop\",\n        isDragging: false,\n        editMode: \"visual\",\n        undoStack: [],\n        redoStack: [],\n        // Basic setters\n        setCurrentPage: (page)=>set({\n                currentPage: page\n            }),\n        selectElement: (element)=>set({\n                selectedElement: element,\n                selectedElements: element ? [\n                    element\n                ] : []\n            }),\n        selectSection: (section)=>set({\n                selectedSection: section\n            }),\n        // Multi-selection actions\n        addToSelection: (element)=>{\n            const state = get();\n            if (!state.selectedElements.find((e)=>e.id === element.id)) {\n                set({\n                    selectedElements: [\n                        ...state.selectedElements,\n                        element\n                    ],\n                    selectedElement: element\n                });\n            }\n        },\n        removeFromSelection: (elementId)=>{\n            const state = get();\n            const newSelection = state.selectedElements.filter((e)=>e.id !== elementId);\n            set({\n                selectedElements: newSelection,\n                selectedElement: newSelection.length > 0 ? newSelection[newSelection.length - 1] : null\n            });\n        },\n        clearSelection: ()=>set({\n                selectedElements: [],\n                selectedElement: null,\n                selectedSection: null\n            }),\n        selectMultiple: (elements)=>set({\n                selectedElements: elements,\n                selectedElement: elements.length > 0 ? elements[elements.length - 1] : null\n            }),\n        // Clipboard operations\n        copyToClipboard: function(elements) {\n            let sections = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n            set({\n                clipboard: {\n                    elements: elements.map((el)=>({\n                            ...el,\n                            id: \"\".concat(el.id, \"_copy\")\n                        })),\n                    sections: sections.map((sec)=>({\n                            ...sec,\n                            id: \"\".concat(sec.id, \"_copy\")\n                        }))\n                }\n            });\n        },\n        pasteFromClipboard: (targetSectionId)=>{\n            const state = get();\n            if (!state.currentPage || !targetSectionId) return;\n            const { elements } = state.clipboard;\n            if (elements.length === 0) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const targetSection = newPage.sections.find((s)=>s.id === targetSectionId);\n            if (targetSection) {\n                elements.forEach((element)=>{\n                    const newElement = {\n                        ...element,\n                        id: \"element_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9))\n                    };\n                    targetSection.elements.push(newElement);\n                });\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"add\",\n                    target: \"element\",\n                    data: {\n                        elements,\n                        targetSectionId\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        // Element operations\n        addElement: (element, sectionId)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const section = newPage.sections.find((s)=>s.id === sectionId);\n            if (section) {\n                section.elements.push(element);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"add\",\n                    target: \"element\",\n                    data: {\n                        element,\n                        sectionId\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        updateElement: (id, props)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const element = get().getElementById(id);\n            if (element) {\n                Object.assign(element, props);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"update\",\n                    target: \"element\",\n                    data: {\n                        id,\n                        props\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        updateMultipleElements: (updates)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            let hasChanges = false;\n            updates.forEach((param)=>{\n                let { id, props } = param;\n                const element = get().getElementById(id);\n                if (element) {\n                    Object.assign(element, props);\n                    hasChanges = true;\n                }\n            });\n            if (hasChanges) {\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"update\",\n                    target: \"element\",\n                    data: {\n                        updates\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        deleteElement: (id)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            for (const section of newPage.sections){\n                const index = section.elements.findIndex((e)=>e.id === id);\n                if (index !== -1) {\n                    var _state_selectedElement;\n                    const deletedElement = section.elements.splice(index, 1)[0];\n                    set({\n                        currentPage: newPage,\n                        selectedElement: ((_state_selectedElement = state.selectedElement) === null || _state_selectedElement === void 0 ? void 0 : _state_selectedElement.id) === id ? null : state.selectedElement,\n                        selectedElements: state.selectedElements.filter((e)=>e.id !== id)\n                    });\n                    // Add to history\n                    get().addToHistory({\n                        type: \"delete\",\n                        target: \"element\",\n                        data: {\n                            element: deletedElement,\n                            sectionId: section.id\n                        },\n                        timestamp: Date.now()\n                    });\n                    break;\n                }\n            }\n        },\n        deleteMultipleElements: (ids)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const deletedElements = [];\n            for (const section of newPage.sections){\n                for(let i = section.elements.length - 1; i >= 0; i--){\n                    const element = section.elements[i];\n                    if (ids.includes(element.id)) {\n                        const deletedElement = section.elements.splice(i, 1)[0];\n                        deletedElements.push({\n                            element: deletedElement,\n                            sectionId: section.id\n                        });\n                    }\n                }\n            }\n            if (deletedElements.length > 0) {\n                set({\n                    currentPage: newPage,\n                    selectedElement: null,\n                    selectedElements: []\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"delete\",\n                    target: \"element\",\n                    data: {\n                        deletedElements\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        duplicateElement: (id)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const element = get().getElementById(id);\n            if (!element) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            for (const section of newPage.sections){\n                const index = section.elements.findIndex((e)=>e.id === id);\n                if (index !== -1) {\n                    const duplicatedElement = {\n                        ...element,\n                        id: \"element_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9))\n                    };\n                    section.elements.splice(index + 1, 0, duplicatedElement);\n                    set({\n                        currentPage: newPage,\n                        selectedElement: duplicatedElement\n                    });\n                    // Add to history\n                    get().addToHistory({\n                        type: \"add\",\n                        target: \"element\",\n                        data: {\n                            element: duplicatedElement,\n                            sectionId: section.id\n                        },\n                        timestamp: Date.now()\n                    });\n                    break;\n                }\n            }\n        },\n        moveElement: (elementId, newParentId, index)=>{\n            // Implementation for moving elements between sections\n            const state = get();\n            if (!state.currentPage) return;\n            // Add to history\n            get().addToHistory({\n                type: \"move\",\n                target: \"element\",\n                data: {\n                    elementId,\n                    newParentId,\n                    index\n                },\n                timestamp: Date.now()\n            });\n        },\n        // Section operations\n        addSection: (section, index)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            if (index !== undefined) {\n                newPage.sections.splice(index, 0, section);\n            } else {\n                newPage.sections.push(section);\n            }\n            set({\n                currentPage: newPage\n            });\n            // Add to history\n            get().addToHistory({\n                type: \"add\",\n                target: \"section\",\n                data: {\n                    section,\n                    index\n                },\n                timestamp: Date.now()\n            });\n        },\n        updateSection: (id, props)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const section = newPage.sections.find((s)=>s.id === id);\n            if (section) {\n                Object.assign(section, props);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"update\",\n                    target: \"section\",\n                    data: {\n                        id,\n                        props\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        deleteSection: (id)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const index = newPage.sections.findIndex((s)=>s.id === id);\n            if (index !== -1) {\n                var _state_selectedSection;\n                const deletedSection = newPage.sections.splice(index, 1)[0];\n                set({\n                    currentPage: newPage,\n                    selectedSection: ((_state_selectedSection = state.selectedSection) === null || _state_selectedSection === void 0 ? void 0 : _state_selectedSection.id) === id ? null : state.selectedSection\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"delete\",\n                    target: \"section\",\n                    data: {\n                        section: deletedSection,\n                        index\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        moveSection: (sectionId, newIndex)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const currentIndex = newPage.sections.findIndex((s)=>s.id === sectionId);\n            if (currentIndex !== -1) {\n                const section = newPage.sections.splice(currentIndex, 1)[0];\n                newPage.sections.splice(newIndex, 0, section);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"move\",\n                    target: \"section\",\n                    data: {\n                        sectionId,\n                        currentIndex,\n                        newIndex\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        // Editor controls\n        setPreviewMode: (isPreview)=>set({\n                isPreviewMode: isPreview\n            }),\n        setBreakpoint: (breakpoint)=>set({\n                currentBreakpoint: breakpoint\n            }),\n        setDragging: (isDragging)=>set({\n                isDragging\n            }),\n        // History operations\n        undo: ()=>{\n            const state = get();\n            if (state.undoStack.length === 0) return;\n            const action = state.undoStack[state.undoStack.length - 1];\n            const newUndoStack = state.undoStack.slice(0, -1);\n            const newRedoStack = [\n                ...state.redoStack,\n                action\n            ];\n            // Reverse the action\n            // Implementation depends on action type\n            set({\n                undoStack: newUndoStack,\n                redoStack: newRedoStack\n            });\n        },\n        redo: ()=>{\n            const state = get();\n            if (state.redoStack.length === 0) return;\n            const action = state.redoStack[state.redoStack.length - 1];\n            const newRedoStack = state.redoStack.slice(0, -1);\n            const newUndoStack = [\n                ...state.undoStack,\n                action\n            ];\n            // Reapply the action\n            // Implementation depends on action type\n            set({\n                undoStack: newUndoStack,\n                redoStack: newRedoStack\n            });\n        },\n        addToHistory: (action)=>{\n            const state = get();\n            const newUndoStack = [\n                ...state.undoStack,\n                action\n            ];\n            // Limit history size\n            if (newUndoStack.length > 50) {\n                newUndoStack.shift();\n            }\n            set({\n                undoStack: newUndoStack,\n                redoStack: [] // Clear redo stack when new action is added\n            });\n        },\n        // Utility functions\n        getElementById: (id)=>{\n            const state = get();\n            if (!state.currentPage) return null;\n            for (const section of state.currentPage.sections){\n                const element = section.elements.find((e)=>e.id === id);\n                if (element) return element;\n            }\n            return null;\n        },\n        getSectionById: (id)=>{\n            const state = get();\n            if (!state.currentPage) return null;\n            return state.currentPage.sections.find((s)=>s.id === id) || null;\n        }\n    }), {\n    name: \"editor-store\"\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/stores/editorStore.ts\n"));

/***/ })

});