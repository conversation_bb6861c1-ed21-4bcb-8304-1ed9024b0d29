"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/demo/page",{

/***/ "(app-pages-browser)/./lib/stores/editorStore.ts":
/*!***********************************!*\
  !*** ./lib/stores/editorStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditorStore: function() { return /* binding */ useEditorStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/../../node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/../../node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useEditorStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((set, get)=>({\n        // Initial state\n        currentPage: null,\n        selectedElement: null,\n        selectedSection: null,\n        isPreviewMode: false,\n        currentBreakpoint: \"desktop\",\n        isDragging: false,\n        undoStack: [],\n        redoStack: [],\n        // Basic setters\n        setCurrentPage: (page)=>set({\n                currentPage: page\n            }),\n        selectElement: (element)=>set({\n                selectedElement: element\n            }),\n        selectSection: (section)=>set({\n                selectedSection: section\n            }),\n        // Element operations\n        addElement: (element, sectionId)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const section = newPage.sections.find((s)=>s.id === sectionId);\n            if (section) {\n                section.elements.push(element);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"add\",\n                    target: \"element\",\n                    data: {\n                        element,\n                        sectionId\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        updateElement: (id, props)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const element = get().getElementById(id);\n            if (element) {\n                Object.assign(element, props);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"update\",\n                    target: \"element\",\n                    data: {\n                        id,\n                        props\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        deleteElement: (id)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            for (const section of newPage.sections){\n                const index = section.elements.findIndex((e)=>e.id === id);\n                if (index !== -1) {\n                    var _state_selectedElement;\n                    const deletedElement = section.elements.splice(index, 1)[0];\n                    set({\n                        currentPage: newPage,\n                        selectedElement: ((_state_selectedElement = state.selectedElement) === null || _state_selectedElement === void 0 ? void 0 : _state_selectedElement.id) === id ? null : state.selectedElement\n                    });\n                    // Add to history\n                    get().addToHistory({\n                        type: \"delete\",\n                        target: \"element\",\n                        data: {\n                            element: deletedElement,\n                            sectionId: section.id\n                        },\n                        timestamp: Date.now()\n                    });\n                    break;\n                }\n            }\n        },\n        moveElement: (elementId, newParentId, index)=>{\n            // Implementation for moving elements between sections\n            const state = get();\n            if (!state.currentPage) return;\n            // Add to history\n            get().addToHistory({\n                type: \"move\",\n                target: \"element\",\n                data: {\n                    elementId,\n                    newParentId,\n                    index\n                },\n                timestamp: Date.now()\n            });\n        },\n        // Section operations\n        addSection: (section, index)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            if (index !== undefined) {\n                newPage.sections.splice(index, 0, section);\n            } else {\n                newPage.sections.push(section);\n            }\n            set({\n                currentPage: newPage\n            });\n            // Add to history\n            get().addToHistory({\n                type: \"add\",\n                target: \"section\",\n                data: {\n                    section,\n                    index\n                },\n                timestamp: Date.now()\n            });\n        },\n        updateSection: (id, props)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const section = newPage.sections.find((s)=>s.id === id);\n            if (section) {\n                Object.assign(section, props);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"update\",\n                    target: \"section\",\n                    data: {\n                        id,\n                        props\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        deleteSection: (id)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const index = newPage.sections.findIndex((s)=>s.id === id);\n            if (index !== -1) {\n                var _state_selectedSection;\n                const deletedSection = newPage.sections.splice(index, 1)[0];\n                set({\n                    currentPage: newPage,\n                    selectedSection: ((_state_selectedSection = state.selectedSection) === null || _state_selectedSection === void 0 ? void 0 : _state_selectedSection.id) === id ? null : state.selectedSection\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"delete\",\n                    target: \"section\",\n                    data: {\n                        section: deletedSection,\n                        index\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        moveSection: (sectionId, newIndex)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const currentIndex = newPage.sections.findIndex((s)=>s.id === sectionId);\n            if (currentIndex !== -1) {\n                const section = newPage.sections.splice(currentIndex, 1)[0];\n                newPage.sections.splice(newIndex, 0, section);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"move\",\n                    target: \"section\",\n                    data: {\n                        sectionId,\n                        currentIndex,\n                        newIndex\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        // Editor controls\n        setPreviewMode: (isPreview)=>set({\n                isPreviewMode: isPreview\n            }),\n        setBreakpoint: (breakpoint)=>set({\n                currentBreakpoint: breakpoint\n            }),\n        setDragging: (isDragging)=>set({\n                isDragging\n            }),\n        // History operations\n        undo: ()=>{\n            const state = get();\n            if (state.undoStack.length === 0) return;\n            const action = state.undoStack[state.undoStack.length - 1];\n            const newUndoStack = state.undoStack.slice(0, -1);\n            const newRedoStack = [\n                ...state.redoStack,\n                action\n            ];\n            // Reverse the action\n            // Implementation depends on action type\n            set({\n                undoStack: newUndoStack,\n                redoStack: newRedoStack\n            });\n        },\n        redo: ()=>{\n            const state = get();\n            if (state.redoStack.length === 0) return;\n            const action = state.redoStack[state.redoStack.length - 1];\n            const newRedoStack = state.redoStack.slice(0, -1);\n            const newUndoStack = [\n                ...state.undoStack,\n                action\n            ];\n            // Reapply the action\n            // Implementation depends on action type\n            set({\n                undoStack: newUndoStack,\n                redoStack: newRedoStack\n            });\n        },\n        addToHistory: (action)=>{\n            const state = get();\n            const newUndoStack = [\n                ...state.undoStack,\n                action\n            ];\n            // Limit history size\n            if (newUndoStack.length > 50) {\n                newUndoStack.shift();\n            }\n            set({\n                undoStack: newUndoStack,\n                redoStack: [] // Clear redo stack when new action is added\n            });\n        },\n        // Utility functions\n        getElementById: (id)=>{\n            const state = get();\n            if (!state.currentPage) return null;\n            for (const section of state.currentPage.sections){\n                const element = section.elements.find((e)=>e.id === id);\n                if (element) return element;\n            }\n            return null;\n        },\n        getSectionById: (id)=>{\n            const state = get();\n            if (!state.currentPage) return null;\n            return state.currentPage.sections.find((s)=>s.id === id) || null;\n        }\n    }), {\n    name: \"editor-store\"\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/stores/editorStore.ts\n"));

/***/ })

});