"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/demo/page",{

/***/ "(app-pages-browser)/./lib/stores/editorStore.ts":
/*!***********************************!*\
  !*** ./lib/stores/editorStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditorStore: function() { return /* binding */ useEditorStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/../../node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/../../node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useEditorStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((set, get)=>({\n        // Initial state\n        currentPage: null,\n        selectedElement: null,\n        selectedSection: null,\n        selectedElements: [],\n        clipboard: {\n            elements: [],\n            sections: []\n        },\n        isPreviewMode: false,\n        currentBreakpoint: \"desktop\",\n        isDragging: false,\n        editMode: \"visual\",\n        undoStack: [],\n        redoStack: [],\n        // Basic setters\n        setCurrentPage: (page)=>set({\n                currentPage: page\n            }),\n        selectElement: (element)=>set({\n                selectedElement: element\n            }),\n        selectSection: (section)=>set({\n                selectedSection: section\n            }),\n        // Element operations\n        addElement: (element, sectionId)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const section = newPage.sections.find((s)=>s.id === sectionId);\n            if (section) {\n                section.elements.push(element);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"add\",\n                    target: \"element\",\n                    data: {\n                        element,\n                        sectionId\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        updateElement: (id, props)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const element = get().getElementById(id);\n            if (element) {\n                Object.assign(element, props);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"update\",\n                    target: \"element\",\n                    data: {\n                        id,\n                        props\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        deleteElement: (id)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            for (const section of newPage.sections){\n                const index = section.elements.findIndex((e)=>e.id === id);\n                if (index !== -1) {\n                    var _state_selectedElement;\n                    const deletedElement = section.elements.splice(index, 1)[0];\n                    set({\n                        currentPage: newPage,\n                        selectedElement: ((_state_selectedElement = state.selectedElement) === null || _state_selectedElement === void 0 ? void 0 : _state_selectedElement.id) === id ? null : state.selectedElement\n                    });\n                    // Add to history\n                    get().addToHistory({\n                        type: \"delete\",\n                        target: \"element\",\n                        data: {\n                            element: deletedElement,\n                            sectionId: section.id\n                        },\n                        timestamp: Date.now()\n                    });\n                    break;\n                }\n            }\n        },\n        moveElement: (elementId, newParentId, index)=>{\n            // Implementation for moving elements between sections\n            const state = get();\n            if (!state.currentPage) return;\n            // Add to history\n            get().addToHistory({\n                type: \"move\",\n                target: \"element\",\n                data: {\n                    elementId,\n                    newParentId,\n                    index\n                },\n                timestamp: Date.now()\n            });\n        },\n        // Section operations\n        addSection: (section, index)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            if (index !== undefined) {\n                newPage.sections.splice(index, 0, section);\n            } else {\n                newPage.sections.push(section);\n            }\n            set({\n                currentPage: newPage\n            });\n            // Add to history\n            get().addToHistory({\n                type: \"add\",\n                target: \"section\",\n                data: {\n                    section,\n                    index\n                },\n                timestamp: Date.now()\n            });\n        },\n        updateSection: (id, props)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const section = newPage.sections.find((s)=>s.id === id);\n            if (section) {\n                Object.assign(section, props);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"update\",\n                    target: \"section\",\n                    data: {\n                        id,\n                        props\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        deleteSection: (id)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const index = newPage.sections.findIndex((s)=>s.id === id);\n            if (index !== -1) {\n                var _state_selectedSection;\n                const deletedSection = newPage.sections.splice(index, 1)[0];\n                set({\n                    currentPage: newPage,\n                    selectedSection: ((_state_selectedSection = state.selectedSection) === null || _state_selectedSection === void 0 ? void 0 : _state_selectedSection.id) === id ? null : state.selectedSection\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"delete\",\n                    target: \"section\",\n                    data: {\n                        section: deletedSection,\n                        index\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        moveSection: (sectionId, newIndex)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const currentIndex = newPage.sections.findIndex((s)=>s.id === sectionId);\n            if (currentIndex !== -1) {\n                const section = newPage.sections.splice(currentIndex, 1)[0];\n                newPage.sections.splice(newIndex, 0, section);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"move\",\n                    target: \"section\",\n                    data: {\n                        sectionId,\n                        currentIndex,\n                        newIndex\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        // Editor controls\n        setPreviewMode: (isPreview)=>set({\n                isPreviewMode: isPreview\n            }),\n        setBreakpoint: (breakpoint)=>set({\n                currentBreakpoint: breakpoint\n            }),\n        setDragging: (isDragging)=>set({\n                isDragging\n            }),\n        // History operations\n        undo: ()=>{\n            const state = get();\n            if (state.undoStack.length === 0) return;\n            const action = state.undoStack[state.undoStack.length - 1];\n            const newUndoStack = state.undoStack.slice(0, -1);\n            const newRedoStack = [\n                ...state.redoStack,\n                action\n            ];\n            // Reverse the action\n            // Implementation depends on action type\n            set({\n                undoStack: newUndoStack,\n                redoStack: newRedoStack\n            });\n        },\n        redo: ()=>{\n            const state = get();\n            if (state.redoStack.length === 0) return;\n            const action = state.redoStack[state.redoStack.length - 1];\n            const newRedoStack = state.redoStack.slice(0, -1);\n            const newUndoStack = [\n                ...state.undoStack,\n                action\n            ];\n            // Reapply the action\n            // Implementation depends on action type\n            set({\n                undoStack: newUndoStack,\n                redoStack: newRedoStack\n            });\n        },\n        addToHistory: (action)=>{\n            const state = get();\n            const newUndoStack = [\n                ...state.undoStack,\n                action\n            ];\n            // Limit history size\n            if (newUndoStack.length > 50) {\n                newUndoStack.shift();\n            }\n            set({\n                undoStack: newUndoStack,\n                redoStack: [] // Clear redo stack when new action is added\n            });\n        },\n        // Utility functions\n        getElementById: (id)=>{\n            const state = get();\n            if (!state.currentPage) return null;\n            for (const section of state.currentPage.sections){\n                const element = section.elements.find((e)=>e.id === id);\n                if (element) return element;\n            }\n            return null;\n        },\n        getSectionById: (id)=>{\n            const state = get();\n            if (!state.currentPage) return null;\n            return state.currentPage.sections.find((s)=>s.id === id) || null;\n        }\n    }), {\n    name: \"editor-store\"\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/stores/editorStore.ts\n"));

/***/ })

});