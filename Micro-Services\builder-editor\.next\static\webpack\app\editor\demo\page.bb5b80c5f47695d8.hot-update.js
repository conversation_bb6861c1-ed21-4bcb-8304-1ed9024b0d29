"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/demo/page",{

/***/ "(app-pages-browser)/./app/editor/components/AutoSave/AutoSave.tsx":
/*!*****************************************************!*\
  !*** ./app/editor/components/AutoSave/AutoSave.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoSave: function() { return /* binding */ AutoSave; },\n/* harmony export */   useManualSave: function() { return /* binding */ useManualSave; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/toast/use-toast.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/color-mode/color-mode-context.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/spinner/spinner.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Check.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Warning.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Time.mjs\");\n/* harmony import */ var _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/editorStore */ \"(app-pages-browser)/./lib/stores/editorStore.ts\");\n/* __next_internal_client_entry_do_not_use__ AutoSave,useManualSave,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\nfunction AutoSave(param) {\n    let { interval = 30000, enabled = true } = param;\n    _s();\n    const [saveStatus, setSaveStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saveError, setSaveError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const bgColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue)(\"white\", \"gray.800\");\n    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue)(\"gray.200\", \"gray.700\");\n    const { currentPage, undoStack, addToHistory } = (0,_lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__.useEditorStore)();\n    // Track changes by monitoring the undo stack\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (undoStack.length > 0) {\n            const lastAction = undoStack[undoStack.length - 1];\n            const timeSinceLastSave = lastSaved ? Date.now() - lastSaved.getTime() : Infinity;\n            // Mark as having unsaved changes if there's been an action since last save\n            if (timeSinceLastSave > 1000) {\n                setHasUnsavedChanges(true);\n                setSaveStatus(\"pending\");\n            }\n        }\n    }, [\n        undoStack,\n        lastSaved\n    ]);\n    const saveToSupabase = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!currentPage) {\n            throw new Error(\"No page to save\");\n        }\n        // Simulate API call to Supabase\n        // In real implementation, this would save to your Supabase database\n        const response = await fetch(\"/api/pages/save\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                pageId: currentPage.id,\n                pageData: currentPage,\n                timestamp: new Date().toISOString()\n            })\n        });\n        if (!response.ok) {\n            throw new Error(\"Save failed: \".concat(response.statusText));\n        }\n        return await response.json();\n    }, [\n        currentPage\n    ]);\n    const performSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        if (!hasUnsavedChanges || !currentPage || saveStatus === \"saving\") {\n            return;\n        }\n        setSaveStatus(\"saving\");\n        setSaveError(null);\n        try {\n            await saveToSupabase();\n            setLastSaved(new Date());\n            setHasUnsavedChanges(false);\n            setSaveStatus(\"saved\");\n            // Reset to idle after showing saved status\n            setTimeout(()=>{\n                setSaveStatus(\"idle\");\n            }, 2000);\n        } catch (error) {\n            console.error(\"Auto-save failed:\", error);\n            setSaveError(error instanceof Error ? error.message : \"Save failed\");\n            setSaveStatus(\"error\");\n            // Show error toast\n            toast({\n                title: \"Auto-save failed\",\n                description: error instanceof Error ? error.message : \"Unknown error occurred\",\n                status: \"error\",\n                duration: 5000,\n                isClosable: true\n            });\n            // Reset to pending after showing error\n            setTimeout(()=>{\n                setSaveStatus(\"pending\");\n            }, 3000);\n        }\n    }, [\n        hasUnsavedChanges,\n        currentPage,\n        saveStatus,\n        saveToSupabase,\n        toast\n    ]);\n    // Auto-save interval\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!enabled) return;\n        const autoSaveInterval = setInterval(()=>{\n            if (hasUnsavedChanges && saveStatus !== \"saving\") {\n                performSave();\n            }\n        }, interval);\n        return ()=>clearInterval(autoSaveInterval);\n    }, [\n        enabled,\n        hasUnsavedChanges,\n        saveStatus,\n        interval,\n        performSave\n    ]);\n    // Save on page unload\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleBeforeUnload = (event)=>{\n            if (hasUnsavedChanges) {\n                event.preventDefault();\n                event.returnValue = \"You have unsaved changes. Are you sure you want to leave?\";\n                return event.returnValue;\n            }\n        };\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return ()=>window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n    }, [\n        hasUnsavedChanges\n    ]);\n    // Manual save function (can be called from keyboard shortcuts)\n    const manualSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        await performSave();\n        if (saveStatus !== \"error\") {\n            toast({\n                title: \"Page saved\",\n                description: \"Your changes have been saved successfully\",\n                status: \"success\",\n                duration: 2000,\n                isClosable: true\n            });\n        }\n    }, [\n        performSave,\n        saveStatus,\n        toast\n    ]);\n    // Expose manual save function globally\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        window.manualSave = manualSave;\n        return ()=>{\n            delete window.manualSave;\n        };\n    }, [\n        manualSave\n    ]);\n    const getStatusIcon = ()=>{\n        switch(saveStatus){\n            case \"saving\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Spinner, {\n                    size: \"xs\",\n                    color: \"blue.500\"\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\AutoSave\\\\AutoSave.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 16\n                }, this);\n            case \"saved\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_6__.CheckIcon, {\n                    color: \"green.500\"\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\AutoSave\\\\AutoSave.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 16\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__.WarningIcon, {\n                    color: \"red.500\"\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\AutoSave\\\\AutoSave.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 16\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_8__.TimeIcon, {\n                    color: \"orange.500\"\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\AutoSave\\\\AutoSave.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    const getStatusText = ()=>{\n        switch(saveStatus){\n            case \"saving\":\n                return \"Saving...\";\n            case \"saved\":\n                return \"Saved\";\n            case \"error\":\n                return \"Save failed\";\n            case \"pending\":\n                return \"Unsaved changes\";\n            default:\n                return lastSaved ? \"Last saved \".concat(formatLastSaved()) : \"No changes\";\n        }\n    };\n    const formatLastSaved = ()=>{\n        if (!lastSaved) return \"\";\n        const now = new Date();\n        const diff = now.getTime() - lastSaved.getTime();\n        if (diff < 60000) {\n            return \"just now\";\n        } else if (diff < 3600000) {\n            const minutes = Math.floor(diff / 60000);\n            return \"\".concat(minutes, \"m ago\");\n        } else {\n            return lastSaved.toLocaleTimeString([], {\n                hour: \"2-digit\",\n                minute: \"2-digit\"\n            });\n        }\n    };\n    const getStatusColor = ()=>{\n        switch(saveStatus){\n            case \"saving\":\n                return \"blue.600\";\n            case \"saved\":\n                return \"green.600\";\n            case \"error\":\n                return \"red.600\";\n            case \"pending\":\n                return \"orange.600\";\n            default:\n                return \"gray.600\";\n        }\n    };\n    if (!enabled) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Box, {\n        position: \"fixed\",\n        bottom: 4,\n        right: 4,\n        bg: bgColor,\n        border: \"1px\",\n        borderColor: borderColor,\n        borderRadius: \"md\",\n        px: 3,\n        py: 2,\n        shadow: \"md\",\n        zIndex: 1000,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.HStack, {\n                spacing: 2,\n                children: [\n                    getStatusIcon(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                        fontSize: \"xs\",\n                        color: getStatusColor(),\n                        fontWeight: \"medium\",\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\AutoSave\\\\AutoSave.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\AutoSave\\\\AutoSave.tsx\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this),\n            saveError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Text, {\n                fontSize: \"xs\",\n                color: \"red.500\",\n                mt: 1,\n                children: saveError\n            }, void 0, false, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\AutoSave\\\\AutoSave.tsx\",\n                lineNumber: 258,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\AutoSave\\\\AutoSave.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_s(AutoSave, \"2D7hNxREXFB1JBuGmP6gt3tLZfM=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useToast,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue,\n        _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__.useEditorStore\n    ];\n});\n_c = AutoSave;\n// Hook for manual save functionality\nconst useManualSave = ()=>{\n    _s1();\n    const performSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (window.manualSave) {\n            window.manualSave();\n        }\n    }, []);\n    return {\n        performSave\n    };\n};\n_s1(useManualSave, \"JtQbWUxybx3zxKWBD1xn6/+3i/A=\");\n/* harmony default export */ __webpack_exports__[\"default\"] = (AutoSave);\nvar _c;\n$RefreshReg$(_c, \"AutoSave\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/editor/components/AutoSave/AutoSave.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/editor/components/History/HistoryPanel.tsx":
/*!********************************************************!*\
  !*** ./app/editor/components/History/HistoryPanel.tsx ***!
  \********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HistoryPanel: function() { return /* binding */ HistoryPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/hooks/dist/esm/use-disclosure.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/color-mode/color-mode-context.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/stack/v-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/badge/badge.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/icon-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/transition/collapse.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Add.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Edit.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Delete.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/DragHandle.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/ChevronUp.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/ChevronDown.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Time.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Repeat.mjs\");\n/* harmony import */ var _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/editorStore */ \"(app-pages-browser)/./lib/stores/editorStore.ts\");\n/* __next_internal_client_entry_do_not_use__ HistoryPanel,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction HistoryPanel() {\n    var _this = this;\n    _s();\n    const [expandedActions, setExpandedActions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const { isOpen, onToggle } = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)({\n        defaultIsOpen: true\n    });\n    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue)(\"gray.200\", \"gray.700\");\n    const bgColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue)(\"white\", \"gray.800\");\n    const hoverBg = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue)(\"gray.50\", \"gray.700\");\n    const { undoStack, redoStack, undo, redo } = (0,_lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__.useEditorStore)();\n    const toggleActionExpansion = (index)=>{\n        const newExpanded = new Set(expandedActions);\n        if (newExpanded.has(index)) {\n            newExpanded.delete(index);\n        } else {\n            newExpanded.add(index);\n        }\n        setExpandedActions(newExpanded);\n    };\n    const getActionIcon = (action)=>{\n        switch(action.type){\n            case \"add\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_5__.AddIcon, {\n                    color: \"green.500\"\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 16\n                }, this);\n            case \"update\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_6__.EditIcon, {\n                    color: \"blue.500\"\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 16\n                }, this);\n            case \"delete\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__.DeleteIcon, {\n                    color: \"red.500\"\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 16\n                }, this);\n            case \"move\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_8__.DragHandleIcon, {\n                    color: \"purple.500\"\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_6__.EditIcon, {\n                    color: \"gray.500\"\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getActionDescription = (action)=>{\n        const target = action.target === \"element\" ? \"Element\" : \"Section\";\n        switch(action.type){\n            case \"add\":\n                if (action.data.elements && action.data.elements.length > 1) {\n                    return \"Added \".concat(action.data.elements.length, \" elements\");\n                }\n                return \"Added \".concat(target);\n            case \"update\":\n                if (action.data.updates && action.data.updates.length > 1) {\n                    return \"Updated \".concat(action.data.updates.length, \" elements\");\n                }\n                return \"Updated \".concat(target);\n            case \"delete\":\n                if (action.data.deletedElements && action.data.deletedElements.length > 1) {\n                    return \"Deleted \".concat(action.data.deletedElements.length, \" elements\");\n                }\n                return \"Deleted \".concat(target);\n            case \"move\":\n                return \"Moved \".concat(target);\n            default:\n                return \"Modified \".concat(target);\n        }\n    };\n    const getActionDetails = (action)=>{\n        var _action_data_element;\n        const details = [];\n        if (action.data.id) {\n            details.push(\"ID: \".concat(action.data.id));\n        }\n        if ((_action_data_element = action.data.element) === null || _action_data_element === void 0 ? void 0 : _action_data_element.type) {\n            details.push(\"Type: \".concat(action.data.element.type));\n        }\n        if (action.data.props) {\n            const propKeys = Object.keys(action.data.props);\n            if (propKeys.length > 0) {\n                details.push(\"Properties: \".concat(propKeys.join(\", \")));\n            }\n        }\n        if (action.data.updates) {\n            details.push(\"Batch update: \".concat(action.data.updates.length, \" items\"));\n        }\n        return details;\n    };\n    const formatTimestamp = (timestamp)=>{\n        const now = Date.now();\n        const diff = now - timestamp;\n        if (diff < 60000) {\n            return \"Just now\";\n        } else if (diff < 3600000) {\n            const minutes = Math.floor(diff / 60000);\n            return \"\".concat(minutes, \"m ago\");\n        } else if (diff < 86400000) {\n            const hours = Math.floor(diff / 3600000);\n            return \"\".concat(hours, \"h ago\");\n        } else {\n            return new Date(timestamp).toLocaleDateString();\n        }\n    };\n    const renderAction = function(action, index) {\n        let isRedo = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const isExpanded = expandedActions.has(index);\n        const details = getActionDetails(action);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Box, {\n            p: 2,\n            border: \"1px\",\n            borderColor: borderColor,\n            borderRadius: \"md\",\n            bg: isRedo ? \"blue.50\" : bgColor,\n            _hover: {\n                bg: hoverBg\n            },\n            opacity: isRedo ? 0.7 : 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.HStack, {\n                    justify: \"space-between\",\n                    align: \"start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.HStack, {\n                            spacing: 2,\n                            flex: 1,\n                            children: [\n                                getActionIcon(action),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.VStack, {\n                                    align: \"start\",\n                                    spacing: 0,\n                                    flex: 1,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                            fontSize: \"sm\",\n                                            fontWeight: \"medium\",\n                                            children: getActionDescription(action)\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, _this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.HStack, {\n                                            spacing: 2,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                                    fontSize: \"xs\",\n                                                    color: \"gray.500\",\n                                                    children: formatTimestamp(action.timestamp)\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 17\n                                                }, _this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                    size: \"sm\",\n                                                    colorScheme: action.type === \"add\" ? \"green\" : action.type === \"delete\" ? \"red\" : \"blue\",\n                                                    children: action.type\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, _this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, _this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, _this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, _this),\n                        details.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.IconButton, {\n                            \"aria-label\": \"Toggle details\",\n                            icon: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_15__.ChevronUpIcon, {}, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 34\n                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_16__.ChevronDownIcon, {}, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 54\n                            }, void 0),\n                            size: \"xs\",\n                            variant: \"ghost\",\n                            onClick: ()=>toggleActionExpansion(index)\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, _this),\n                details.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Collapse, {\n                    in: isExpanded,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Box, {\n                        mt: 2,\n                        pt: 2,\n                        borderTop: \"1px\",\n                        borderColor: borderColor,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.VStack, {\n                            align: \"start\",\n                            spacing: 1,\n                            children: details.map((detail, detailIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                    fontSize: \"xs\",\n                                    color: \"gray.600\",\n                                    children: detail\n                                }, detailIndex, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 19\n                                }, _this))\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 15\n                        }, _this)\n                    }, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 13\n                    }, _this)\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, \"\".concat(isRedo ? \"redo\" : \"undo\", \"-\").concat(index), true, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n            lineNumber: 144,\n            columnNumber: 7\n        }, _this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Box, {\n        w: \"300px\",\n        h: \"100%\",\n        bg: bgColor,\n        borderLeft: \"1px\",\n        borderColor: borderColor,\n        display: \"flex\",\n        flexDirection: \"column\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.HStack, {\n                justify: \"space-between\",\n                p: 3,\n                borderBottom: \"1px\",\n                borderColor: borderColor,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.HStack, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_18__.TimeIcon, {}, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                fontSize: \"sm\",\n                                fontWeight: \"semibold\",\n                                children: \"History\"\n                            }, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.IconButton, {\n                        \"aria-label\": \"Toggle history panel\",\n                        icon: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_15__.ChevronUpIcon, {}, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 26\n                        }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_16__.ChevronDownIcon, {}, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 46\n                        }, void 0),\n                        size: \"xs\",\n                        variant: \"ghost\",\n                        onClick: onToggle\n                    }, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.Collapse, {\n                in: isOpen,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.HStack, {\n                        p: 3,\n                        spacing: 2,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                size: \"sm\",\n                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_20__.RepeatIcon, {\n                                    transform: \"scaleX(-1)\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: undo,\n                                isDisabled: undoStack.length === 0,\n                                flex: 1,\n                                children: \"Undo\"\n                            }, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Button, {\n                                size: \"sm\",\n                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_20__.RepeatIcon, {}, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 23\n                                }, void 0),\n                                onClick: redo,\n                                isDisabled: redoStack.length === 0,\n                                flex: 1,\n                                children: \"Redo\"\n                            }, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Divider, {}, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Box, {\n                        flex: 1,\n                        overflowY: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.VStack, {\n                            spacing: 2,\n                            p: 3,\n                            align: \"stretch\",\n                            children: [\n                                redoStack.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                            fontSize: \"xs\",\n                                            fontWeight: \"semibold\",\n                                            color: \"blue.600\",\n                                            mb: 1,\n                                            children: [\n                                                \"REDO AVAILABLE (\",\n                                                redoStack.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this),\n                                        redoStack.slice().reverse().map((action, index)=>renderAction(action, redoStack.length - 1 - index, true)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.Divider, {\n                                            my: 2\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Box, {\n                                    p: 2,\n                                    bg: \"green.50\",\n                                    border: \"2px\",\n                                    borderColor: \"green.200\",\n                                    borderRadius: \"md\",\n                                    textAlign: \"center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                        fontSize: \"xs\",\n                                        fontWeight: \"semibold\",\n                                        color: \"green.700\",\n                                        children: \"CURRENT STATE\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this),\n                                undoStack.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                            fontSize: \"xs\",\n                                            fontWeight: \"semibold\",\n                                            color: \"gray.600\",\n                                            mt: 2,\n                                            mb: 1,\n                                            children: [\n                                                \"HISTORY (\",\n                                                undoStack.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        undoStack.slice().reverse().map((action, index)=>renderAction(action, undoStack.length - 1 - index, false))\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Box, {\n                                    p: 4,\n                                    textAlign: \"center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                        fontSize: \"sm\",\n                                        color: \"gray.500\",\n                                        children: \"No history yet. Start editing to see your actions here.\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Box, {\n                        p: 3,\n                        borderTop: \"1px\",\n                        borderColor: borderColor,\n                        bg: \"gray.50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.HStack, {\n                            justify: \"space-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                    fontSize: \"xs\",\n                                    color: \"gray.600\",\n                                    children: [\n                                        undoStack.length,\n                                        \" actions\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.Text, {\n                                    fontSize: \"xs\",\n                                    color: \"gray.600\",\n                                    children: [\n                                        redoStack.length,\n                                        \" redo available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\History\\\\HistoryPanel.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n_s(HistoryPanel, \"khH/uEu5iNLYCjEF0sZ9/YzbvA8=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue,\n        _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__.useEditorStore\n    ];\n});\n_c = HistoryPanel;\n/* harmony default export */ __webpack_exports__[\"default\"] = (HistoryPanel);\nvar _c;\n$RefreshReg$(_c, \"HistoryPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/editor/components/History/HistoryPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/editor/components/KeyboardShortcuts/KeyboardShortcuts.tsx":
/*!***********************************************************************!*\
  !*** ./app/editor/components/KeyboardShortcuts/KeyboardShortcuts.tsx ***!
  \***********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeyboardShortcuts: function() { return /* binding */ KeyboardShortcuts; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/hooks/dist/esm/use-disclosure.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/color-mode/color-mode-context.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/kbd/kbd.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/modal/modal.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/modal/modal-overlay.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/modal/modal-content.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/modal/modal-header.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/modal/modal-close-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/modal/modal-body.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/stack/v-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/editorStore */ \"(app-pages-browser)/./lib/stores/editorStore.ts\");\n/* __next_internal_client_entry_do_not_use__ KeyboardShortcuts,default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction KeyboardShortcuts() {\n    _s();\n    const { isOpen, onOpen, onClose } = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useDisclosure)();\n    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue)(\"gray.200\", \"gray.700\");\n    const { undo, redo, copyToClipboard, pasteFromClipboard, deleteElement, deleteMultipleElements, duplicateElement, selectedElement, selectedElements, setPreviewMode, isPreviewMode, clearSelection } = (0,_lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__.useEditorStore)();\n    const shortcuts = [\n        // File Operations\n        {\n            keys: [\n                \"Ctrl\",\n                \"S\"\n            ],\n            description: \"Save page\",\n            action: ()=>{\n                // TODO: Implement save functionality\n                console.log(\"Save triggered\");\n            },\n            category: \"File\"\n        },\n        {\n            keys: [\n                \"Ctrl\",\n                \"Shift\",\n                \"S\"\n            ],\n            description: \"Save as template\",\n            action: ()=>{\n                // TODO: Implement save as template\n                console.log(\"Save as template triggered\");\n            },\n            category: \"File\"\n        },\n        // Edit Operations\n        {\n            keys: [\n                \"Ctrl\",\n                \"Z\"\n            ],\n            description: \"Undo\",\n            action: undo,\n            category: \"Edit\"\n        },\n        {\n            keys: [\n                \"Ctrl\",\n                \"Y\"\n            ],\n            description: \"Redo\",\n            action: redo,\n            category: \"Edit\"\n        },\n        {\n            keys: [\n                \"Ctrl\",\n                \"C\"\n            ],\n            description: \"Copy selected elements\",\n            action: ()=>{\n                if (selectedElements.length > 0) {\n                    copyToClipboard(selectedElements);\n                } else if (selectedElement) {\n                    copyToClipboard([\n                        selectedElement\n                    ]);\n                }\n            },\n            category: \"Edit\"\n        },\n        {\n            keys: [\n                \"Ctrl\",\n                \"V\"\n            ],\n            description: \"Paste elements\",\n            action: ()=>{\n                // TODO: Get current section ID for pasting\n                pasteFromClipboard();\n            },\n            category: \"Edit\"\n        },\n        {\n            keys: [\n                \"Ctrl\",\n                \"D\"\n            ],\n            description: \"Duplicate selected element\",\n            action: ()=>{\n                if (selectedElement) {\n                    duplicateElement(selectedElement.id);\n                }\n            },\n            category: \"Edit\"\n        },\n        {\n            keys: [\n                \"Delete\"\n            ],\n            description: \"Delete selected elements\",\n            action: ()=>{\n                if (selectedElements.length > 1) {\n                    deleteMultipleElements(selectedElements.map((el)=>el.id));\n                } else if (selectedElement) {\n                    deleteElement(selectedElement.id);\n                }\n            },\n            category: \"Edit\"\n        },\n        {\n            keys: [\n                \"Escape\"\n            ],\n            description: \"Clear selection\",\n            action: clearSelection,\n            category: \"Edit\"\n        },\n        // View Operations\n        {\n            keys: [\n                \"Ctrl\",\n                \"Shift\",\n                \"P\"\n            ],\n            description: \"Toggle preview mode\",\n            action: ()=>setPreviewMode(!isPreviewMode),\n            category: \"View\"\n        },\n        {\n            keys: [\n                \"?\"\n            ],\n            description: \"Show keyboard shortcuts\",\n            action: onOpen,\n            category: \"Help\"\n        },\n        // Selection Operations\n        {\n            keys: [\n                \"Ctrl\",\n                \"A\"\n            ],\n            description: \"Select all elements\",\n            action: ()=>{\n                // TODO: Implement select all\n                console.log(\"Select all triggered\");\n            },\n            category: \"Selection\"\n        },\n        // Navigation\n        {\n            keys: [\n                \"Tab\"\n            ],\n            description: \"Select next element\",\n            action: ()=>{\n                // TODO: Implement tab navigation\n                console.log(\"Tab navigation triggered\");\n            },\n            category: \"Navigation\"\n        },\n        {\n            keys: [\n                \"Shift\",\n                \"Tab\"\n            ],\n            description: \"Select previous element\",\n            action: ()=>{\n                // TODO: Implement shift+tab navigation\n                console.log(\"Shift+Tab navigation triggered\");\n            },\n            category: \"Navigation\"\n        }\n    ];\n    const handleKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((event)=>{\n        // Don't trigger shortcuts when typing in inputs\n        if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement || event.target.contentEditable === \"true\") {\n            return;\n        }\n        const pressedKeys = [];\n        if (event.ctrlKey || event.metaKey) pressedKeys.push(\"Ctrl\");\n        if (event.shiftKey) pressedKeys.push(\"Shift\");\n        if (event.altKey) pressedKeys.push(\"Alt\");\n        // Add the main key\n        if (event.key === \" \") {\n            pressedKeys.push(\"Space\");\n        } else if (event.key === \"Escape\") {\n            pressedKeys.push(\"Escape\");\n        } else if (event.key === \"Delete\") {\n            pressedKeys.push(\"Delete\");\n        } else if (event.key === \"Tab\") {\n            pressedKeys.push(\"Tab\");\n        } else if (event.key.length === 1) {\n            pressedKeys.push(event.key.toUpperCase());\n        }\n        // Find matching shortcut\n        const matchingShortcut = shortcuts.find((shortcut)=>{\n            if (shortcut.keys.length !== pressedKeys.length) return false;\n            return shortcut.keys.every((key)=>pressedKeys.includes(key));\n        });\n        if (matchingShortcut) {\n            event.preventDefault();\n            matchingShortcut.action();\n        }\n    }, [\n        shortcuts,\n        undo,\n        redo,\n        copyToClipboard,\n        pasteFromClipboard,\n        deleteElement,\n        deleteMultipleElements,\n        duplicateElement,\n        selectedElement,\n        selectedElements,\n        setPreviewMode,\n        isPreviewMode,\n        clearSelection,\n        onOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyDown);\n        };\n    }, [\n        handleKeyDown\n    ]);\n    const groupedShortcuts = shortcuts.reduce((acc, shortcut)=>{\n        if (!acc[shortcut.category]) {\n            acc[shortcut.category] = [];\n        }\n        acc[shortcut.category].push(shortcut);\n        return acc;\n    }, {});\n    const renderShortcut = (shortcut)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n            justify: \"space-between\",\n            py: 1,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                    fontSize: \"sm\",\n                    children: shortcut.description\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.HStack, {\n                    spacing: 1,\n                    children: shortcut.keys.map((key, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Kbd, {\n                                    fontSize: \"xs\",\n                                    children: key\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                index < shortcut.keys.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                    fontSize: \"xs\",\n                                    children: \"+\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 50\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, shortcut.description, true, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n            lineNumber: 234,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Modal, {\n            isOpen: isOpen,\n            onClose: onClose,\n            size: \"lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.ModalOverlay, {}, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ModalContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.ModalHeader, {\n                            children: \"Keyboard Shortcuts\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_12__.ModalCloseButton, {}, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.ModalBody, {\n                            pb: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.VStack, {\n                                spacing: 4,\n                                align: \"stretch\",\n                                children: [\n                                    Object.entries(groupedShortcuts).map((param)=>{\n                                        let [category, categoryShortcuts] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Box, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                                    fontSize: \"md\",\n                                                    fontWeight: \"semibold\",\n                                                    color: \"blue.600\",\n                                                    mb: 2,\n                                                    children: category\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.VStack, {\n                                                    spacing: 1,\n                                                    align: \"stretch\",\n                                                    children: categoryShortcuts.map(renderShortcut)\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.Divider, {\n                                                    mt: 3\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, category, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 17\n                                        }, this);\n                                    }),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Box, {\n                                        mt: 4,\n                                        p: 3,\n                                        bg: \"gray.50\",\n                                        borderRadius: \"md\",\n                                        border: \"1px\",\n                                        borderColor: borderColor,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"gray.600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Tip:\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Press \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.Kbd, {\n                                                    children: \"?\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 47\n                                                }, this),\n                                                \" anytime to view these shortcuts. Most shortcuts work when elements are selected and you're not typing in a text field.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\KeyboardShortcuts\\\\KeyboardShortcuts.tsx\",\n            lineNumber: 249,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(KeyboardShortcuts, \"QbtRaRBg13cyxXoG8EMuHo1mcQk=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useDisclosure,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.useColorModeValue,\n        _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__.useEditorStore\n    ];\n});\n_c = KeyboardShortcuts;\n/* harmony default export */ __webpack_exports__[\"default\"] = (KeyboardShortcuts);\nvar _c;\n$RefreshReg$(_c, \"KeyboardShortcuts\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/editor/components/KeyboardShortcuts/KeyboardShortcuts.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/editor/components/Properties/EnhancedElementProperties.tsx":
/*!************************************************************************!*\
  !*** ./app/editor/components/Properties/EnhancedElementProperties.tsx ***!
  \************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnhancedElementProperties: function() { return /* binding */ EnhancedElementProperties; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/color-mode/color-mode-context.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/stack/h-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/stack/v-stack.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/badge/badge.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/tooltip/tooltip.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/icon-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/alert/alert.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/alert/alert-icon.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/form-control/form-control.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/form-control/form-label.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/textarea/textarea.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/input/input.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/select/select.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/button-group.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/tabs/tabs.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/tabs/tab-list.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/tabs/tab.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/tabs/tab-panels.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/tabs/tab-panel.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/slider/slider.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/number-input/number-input.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Copy.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Repeat.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Delete.mjs\");\n/* harmony import */ var _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/stores/editorStore */ \"(app-pages-browser)/./lib/stores/editorStore.ts\");\n/* __next_internal_client_entry_do_not_use__ EnhancedElementProperties,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction EnhancedElementProperties(param) {\n    let { element, elements } = param;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useColorModeValue)(\"gray.200\", \"gray.700\");\n    const bgColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useColorModeValue)(\"white\", \"gray.800\");\n    const { updateElement, updateMultipleElements, deleteElement, deleteMultipleElements, duplicateElement, copyToClipboard, selectedElements, currentBreakpoint } = (0,_lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__.useEditorStore)();\n    const isMultiSelect = elements && elements.length > 1;\n    const targetElement = element || elements && elements[0];\n    const targetElements = elements || (element ? [\n        element\n    ] : []);\n    if (!targetElement) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n            p: 4,\n            textAlign: \"center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                color: \"gray.500\",\n                fontSize: \"sm\",\n                children: \"Select an element to edit properties\"\n            }, void 0, false, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this);\n    }\n    const handleSinglePropChange = (key, value)=>{\n        if (targetElement) {\n            updateElement(targetElement.id, {\n                props: {\n                    ...targetElement.props,\n                    [key]: value\n                }\n            });\n        }\n    };\n    const handleMultiplePropChange = (key, value)=>{\n        if (isMultiSelect && targetElements.length > 0) {\n            const updates = targetElements.map((el)=>({\n                    id: el.id,\n                    props: {\n                        ...el.props,\n                        [key]: value\n                    }\n                }));\n            updateMultipleElements(updates);\n        } else {\n            handleSinglePropChange(key, value);\n        }\n    };\n    const handleSingleStyleChange = (key, value)=>{\n        if (targetElement) {\n            updateElement(targetElement.id, {\n                style: {\n                    ...targetElement.style,\n                    [key]: value\n                }\n            });\n        }\n    };\n    const handleMultipleStyleChange = (key, value)=>{\n        if (isMultiSelect && targetElements.length > 0) {\n            const updates = targetElements.map((el)=>({\n                    id: el.id,\n                    style: {\n                        ...el.style,\n                        [key]: value\n                    }\n                }));\n            updateMultipleElements(updates);\n        } else {\n            handleSingleStyleChange(key, value);\n        }\n    };\n    const handleDelete = ()=>{\n        if (isMultiSelect) {\n            deleteMultipleElements(targetElements.map((el)=>el.id));\n        } else if (targetElement) {\n            deleteElement(targetElement.id);\n        }\n    };\n    const handleDuplicate = ()=>{\n        if (targetElement) {\n            duplicateElement(targetElement.id);\n        }\n    };\n    const handleCopy = ()=>{\n        copyToClipboard(targetElements);\n    };\n    const getCommonValue = (key, source)=>{\n        if (!isMultiSelect || targetElements.length === 0) {\n            return source === \"props\" ? targetElement === null || targetElement === void 0 ? void 0 : targetElement.props[key] : targetElement === null || targetElement === void 0 ? void 0 : targetElement.style[key];\n        }\n        const values = targetElements.map((el)=>source === \"props\" ? el.props[key] : el.style[key]);\n        const firstValue = values[0];\n        const allSame = values.every((val)=>val === firstValue);\n        return allSame ? firstValue : undefined;\n    };\n    const renderElementHeader = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                    justify: \"space-between\",\n                    mb: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                            align: \"start\",\n                            spacing: 0,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                            fontSize: \"sm\",\n                                            fontWeight: \"semibold\",\n                                            color: \"gray.700\",\n                                            children: isMultiSelect ? \"Multiple Elements\" : \"\".concat(targetElement.type.charAt(0).toUpperCase() + targetElement.type.slice(1), \" Element\")\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 13\n                                        }, this),\n                                        isMultiSelect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                            colorScheme: \"blue\",\n                                            size: \"sm\",\n                                            children: [\n                                                targetElements.length,\n                                                \" selected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                                    fontSize: \"xs\",\n                                    color: \"gray.500\",\n                                    children: isMultiSelect ? \"Bulk editing mode\" : \"ID: \".concat(targetElement.id)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                            spacing: 1,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                    label: \"Copy\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.IconButton, {\n                                        \"aria-label\": \"Copy\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_11__.CopyIcon, {}, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        size: \"xs\",\n                                        variant: \"ghost\",\n                                        onClick: handleCopy\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 11\n                                }, this),\n                                !isMultiSelect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                    label: \"Duplicate\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.IconButton, {\n                                        \"aria-label\": \"Duplicate\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_12__.RepeatIcon, {}, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        size: \"xs\",\n                                        variant: \"ghost\",\n                                        onClick: handleDuplicate\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                    label: \"Delete\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.IconButton, {\n                                        \"aria-label\": \"Delete\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__.DeleteIcon, {}, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        size: \"xs\",\n                                        variant: \"ghost\",\n                                        colorScheme: \"red\",\n                                        onClick: handleDelete\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 7\n                }, this),\n                isMultiSelect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Alert, {\n                    status: \"info\",\n                    size: \"sm\",\n                    mb: 3,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.AlertIcon, {}, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                            fontSize: \"xs\",\n                            children: [\n                                \"Changes will be applied to all \",\n                                targetElements.length,\n                                \" selected elements\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n            lineNumber: 177,\n            columnNumber: 5\n        }, this);\n    const renderContentTab = ()=>{\n        const elementType = targetElement.type;\n        switch(elementType){\n            case \"text\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                    spacing: 3,\n                    align: \"stretch\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                    fontSize: \"sm\",\n                                    children: \"Content\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Textarea, {\n                                    value: getCommonValue(\"content\", \"props\") || \"\",\n                                    onChange: (e)=>handleMultiplePropChange(\"content\", e.target.value),\n                                    size: \"sm\",\n                                    rows: 3,\n                                    placeholder: isMultiSelect ? \"Mixed values\" : \"Enter text content\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                            fontSize: \"sm\",\n                                            children: \"Font Size\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                            value: getCommonValue(\"fontSize\", \"props\") || \"\",\n                                            onChange: (e)=>handleMultiplePropChange(\"fontSize\", e.target.value),\n                                            size: \"sm\",\n                                            placeholder: \"16px\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                            fontSize: \"sm\",\n                                            children: \"Font Weight\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Select, {\n                                            value: getCommonValue(\"fontWeight\", \"props\") || \"normal\",\n                                            onChange: (e)=>handleMultiplePropChange(\"fontWeight\", e.target.value),\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"normal\",\n                                                    children: \"Normal\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"bold\",\n                                                    children: \"Bold\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"lighter\",\n                                                    children: \"Light\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"600\",\n                                                    children: \"Semi Bold\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                    fontSize: \"sm\",\n                                    children: \"Text Align\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_21__.ButtonGroup, {\n                                    size: \"sm\",\n                                    isAttached: true,\n                                    children: [\n                                        \"left\",\n                                        \"center\",\n                                        \"right\",\n                                        \"justify\"\n                                    ].map((align)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_22__.Button, {\n                                            variant: getCommonValue(\"textAlign\", \"props\") === align ? \"solid\" : \"outline\",\n                                            onClick: ()=>handleMultiplePropChange(\"textAlign\", align),\n                                            size: \"sm\",\n                                            children: align.charAt(0).toUpperCase()\n                                        }, align, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 11\n                }, this);\n            case \"image\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                    spacing: 3,\n                    align: \"stretch\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                    fontSize: \"sm\",\n                                    children: \"Image URL\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                    value: getCommonValue(\"src\", \"props\") || \"\",\n                                    onChange: (e)=>handleMultiplePropChange(\"src\", e.target.value),\n                                    size: \"sm\",\n                                    placeholder: \"https://example.com/image.jpg\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                    fontSize: \"sm\",\n                                    children: \"Alt Text\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                    value: getCommonValue(\"alt\", \"props\") || \"\",\n                                    onChange: (e)=>handleMultiplePropChange(\"alt\", e.target.value),\n                                    size: \"sm\",\n                                    placeholder: \"Image description\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                            fontSize: \"sm\",\n                                            children: \"Width\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                            value: getCommonValue(\"width\", \"style\") || \"\",\n                                            onChange: (e)=>handleMultipleStyleChange(\"width\", e.target.value),\n                                            size: \"sm\",\n                                            placeholder: \"auto\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                            fontSize: \"sm\",\n                                            children: \"Height\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                            value: getCommonValue(\"height\", \"style\") || \"\",\n                                            onChange: (e)=>handleMultipleStyleChange(\"height\", e.target.value),\n                                            size: \"sm\",\n                                            placeholder: \"auto\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, this);\n            case \"button\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                    spacing: 3,\n                    align: \"stretch\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                    fontSize: \"sm\",\n                                    children: \"Button Text\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                    value: getCommonValue(\"text\", \"props\") || \"\",\n                                    onChange: (e)=>handleMultiplePropChange(\"text\", e.target.value),\n                                    size: \"sm\",\n                                    placeholder: \"Button text\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                            fontSize: \"sm\",\n                                            children: \"Color Scheme\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Select, {\n                                            value: getCommonValue(\"colorScheme\", \"props\") || \"blue\",\n                                            onChange: (e)=>handleMultiplePropChange(\"colorScheme\", e.target.value),\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"blue\",\n                                                    children: \"Blue\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"green\",\n                                                    children: \"Green\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"red\",\n                                                    children: \"Red\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"orange\",\n                                                    children: \"Orange\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"purple\",\n                                                    children: \"Purple\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                            fontSize: \"sm\",\n                                            children: \"Size\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Select, {\n                                            value: getCommonValue(\"size\", \"props\") || \"md\",\n                                            onChange: (e)=>handleMultiplePropChange(\"size\", e.target.value),\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"xs\",\n                                                    children: \"Extra Small\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"sm\",\n                                                    children: \"Small\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"md\",\n                                                    children: \"Medium\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"lg\",\n                                                    children: \"Large\"\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                    fontSize: \"sm\",\n                                    children: \"Link URL\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                    value: getCommonValue(\"href\", \"props\") || \"\",\n                                    onChange: (e)=>handleMultiplePropChange(\"href\", e.target.value),\n                                    size: \"sm\",\n                                    placeholder: \"https://example.com\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                    fontSize: \"sm\",\n                    color: \"gray.500\",\n                    children: isMultiSelect ? \"Mixed element types selected\" : \"No specific properties for \".concat(elementType, \" elements\")\n                }, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Box, {\n        p: 4,\n        bg: bgColor,\n        borderRadius: \"md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n            spacing: 4,\n            align: \"stretch\",\n            children: [\n                renderElementHeader(),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_23__.Divider, {}, void 0, false, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_24__.Tabs, {\n                    index: activeTab,\n                    onChange: setActiveTab,\n                    size: \"sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_25__.TabList, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Tab, {\n                                    children: \"Content\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Tab, {\n                                    children: \"Style\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Tab, {\n                                    children: \"Layout\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_26__.Tab, {\n                                    children: \"Advanced\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_27__.TabPanels, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.TabPanel, {\n                                    px: 0,\n                                    children: renderContentTab()\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.TabPanel, {\n                                    px: 0,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                        spacing: 3,\n                                        align: \"stretch\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                        fontSize: \"sm\",\n                                                        children: \"Background Color\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                                        type: \"color\",\n                                                        value: getCommonValue(\"backgroundColor\", \"style\") || \"#ffffff\",\n                                                        onChange: (e)=>handleMultipleStyleChange(\"backgroundColor\", e.target.value),\n                                                        size: \"sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 436,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                        fontSize: \"sm\",\n                                                        children: \"Text Color\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                                        type: \"color\",\n                                                        value: getCommonValue(\"color\", \"style\") || \"#000000\",\n                                                        onChange: (e)=>handleMultipleStyleChange(\"color\", e.target.value),\n                                                        size: \"sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                        fontSize: \"sm\",\n                                                        children: \"Border Radius\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                                        value: getCommonValue(\"borderRadius\", \"style\") || \"\",\n                                                        onChange: (e)=>handleMultipleStyleChange(\"borderRadius\", e.target.value),\n                                                        size: \"sm\",\n                                                        placeholder: \"0px\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                        fontSize: \"sm\",\n                                                        children: [\n                                                            \"Opacity: \",\n                                                            Math.round((getCommonValue(\"opacity\", \"style\") || 1) * 100),\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__.Slider, {\n                                                        value: (getCommonValue(\"opacity\", \"style\") || 1) * 100,\n                                                        onChange: (val)=>handleMultipleStyleChange(\"opacity\", val / 100),\n                                                        min: 0,\n                                                        max: 100,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__.SliderTrack, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__.SliderFilledTrack, {}, void 0, false, {\n                                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 474,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_29__.SliderThumb, {}, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.TabPanel, {\n                                    px: 0,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                        spacing: 3,\n                                        align: \"stretch\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.HStack, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                                fontSize: \"sm\",\n                                                                children: \"Margin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                                                value: getCommonValue(\"margin\", \"style\") || \"\",\n                                                                onChange: (e)=>handleMultipleStyleChange(\"margin\", e.target.value),\n                                                                size: \"sm\",\n                                                                placeholder: \"0px\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                                fontSize: \"sm\",\n                                                                children: \"Padding\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 497,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                                                value: getCommonValue(\"padding\", \"style\") || \"\",\n                                                                onChange: (e)=>handleMultipleStyleChange(\"padding\", e.target.value),\n                                                                size: \"sm\",\n                                                                placeholder: \"0px\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                        fontSize: \"sm\",\n                                                        children: \"Display\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Select, {\n                                                        value: getCommonValue(\"display\", \"style\") || \"block\",\n                                                        onChange: (e)=>handleMultipleStyleChange(\"display\", e.target.value),\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"block\",\n                                                                children: \"Block\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"inline\",\n                                                                children: \"Inline\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"inline-block\",\n                                                                children: \"Inline Block\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"flex\",\n                                                                children: \"Flex\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"grid\",\n                                                                children: \"Grid\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"none\",\n                                                                children: \"None\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                        fontSize: \"sm\",\n                                                        children: \"Position\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_20__.Select, {\n                                                        value: getCommonValue(\"position\", \"style\") || \"static\",\n                                                        onChange: (e)=>handleMultipleStyleChange(\"position\", e.target.value),\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"static\",\n                                                                children: \"Static\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"relative\",\n                                                                children: \"Relative\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 531,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"absolute\",\n                                                                children: \"Absolute\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"fixed\",\n                                                                children: \"Fixed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"sticky\",\n                                                                children: \"Sticky\"\n                                                            }, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_28__.TabPanel, {\n                                    px: 0,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_7__.VStack, {\n                                        spacing: 3,\n                                        align: \"stretch\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                        fontSize: \"sm\",\n                                                        children: \"Z-Index\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__.NumberInput, {\n                                                        value: getCommonValue(\"zIndex\", \"style\") || 0,\n                                                        onChange: (_, val)=>handleMultipleStyleChange(\"zIndex\", val),\n                                                        size: \"sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__.NumberInputField, {}, void 0, false, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__.NumberInputStepper, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__.NumberIncrementStepper, {}, void 0, false, {\n                                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                        lineNumber: 551,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_30__.NumberDecrementStepper, {}, void 0, false, {\n                                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                        lineNumber: 552,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 542,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                        fontSize: \"sm\",\n                                                        children: \"Transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                                        value: getCommonValue(\"transform\", \"style\") || \"\",\n                                                        onChange: (e)=>handleMultipleStyleChange(\"transform\", e.target.value),\n                                                        size: \"sm\",\n                                                        placeholder: \"rotate(0deg) scale(1)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                        fontSize: \"sm\",\n                                                        children: \"Transition\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_19__.Input, {\n                                                        value: getCommonValue(\"transition\", \"style\") || \"\",\n                                                        onChange: (e)=>handleMultipleStyleChange(\"transition\", e.target.value),\n                                                        size: \"sm\",\n                                                        placeholder: \"all 0.3s ease\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_16__.FormControl, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.FormLabel, {\n                                                        fontSize: \"sm\",\n                                                        children: \"Custom CSS\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.Textarea, {\n                                                        value: getCommonValue(\"customCSS\", \"style\") || \"\",\n                                                        onChange: (e)=>handleMultipleStyleChange(\"customCSS\", e.target.value),\n                                                        size: \"sm\",\n                                                        rows: 3,\n                                                        placeholder: \"Custom CSS properties\"\n                                                    }, void 0, false, {\n                                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n                    lineNumber: 421,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n            lineNumber: 416,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Properties\\\\EnhancedElementProperties.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, this);\n}\n_s(EnhancedElementProperties, \"kTNRud/vmXDIav0GwiFoNJQ152E=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useColorModeValue,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useColorModeValue,\n        _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_2__.useEditorStore\n    ];\n});\n_c = EnhancedElementProperties;\n/* harmony default export */ __webpack_exports__[\"default\"] = (EnhancedElementProperties);\nvar _c;\n$RefreshReg$(_c, \"EnhancedElementProperties\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/editor/components/Properties/EnhancedElementProperties.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/editor/page.tsx":
/*!*****************************!*\
  !*** ./app/editor/page.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditorPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/color-mode/color-mode-context.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _components_Canvas_DragDropCanvas__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/Canvas/DragDropCanvas */ \"(app-pages-browser)/./app/editor/components/Canvas/DragDropCanvas.tsx\");\n/* harmony import */ var _components_Palette_ComponentPalette__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/Palette/ComponentPalette */ \"(app-pages-browser)/./app/editor/components/Palette/ComponentPalette.tsx\");\n/* harmony import */ var _components_Properties_PropertyPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/Properties/PropertyPanel */ \"(app-pages-browser)/./app/editor/components/Properties/PropertyPanel.tsx\");\n/* harmony import */ var _components_Layers_LayersPanel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/Layers/LayersPanel */ \"(app-pages-browser)/./app/editor/components/Layers/LayersPanel.tsx\");\n/* harmony import */ var _components_Toolbar_EditorToolbar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/Toolbar/EditorToolbar */ \"(app-pages-browser)/./app/editor/components/Toolbar/EditorToolbar.tsx\");\n/* harmony import */ var _components_Preview_ResponsivePreview__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/Preview/ResponsivePreview */ \"(app-pages-browser)/./app/editor/components/Preview/ResponsivePreview.tsx\");\n/* harmony import */ var _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/stores/editorStore */ \"(app-pages-browser)/./lib/stores/editorStore.ts\");\n/* harmony import */ var _components_Properties_EnhancedElementProperties__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/Properties/EnhancedElementProperties */ \"(app-pages-browser)/./app/editor/components/Properties/EnhancedElementProperties.tsx\");\n/* harmony import */ var _components_KeyboardShortcuts_KeyboardShortcuts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/KeyboardShortcuts/KeyboardShortcuts */ \"(app-pages-browser)/./app/editor/components/KeyboardShortcuts/KeyboardShortcuts.tsx\");\n/* harmony import */ var _components_History_HistoryPanel__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./components/History/HistoryPanel */ \"(app-pages-browser)/./app/editor/components/History/HistoryPanel.tsx\");\n/* harmony import */ var _components_AutoSave_AutoSave__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./components/AutoSave/AutoSave */ \"(app-pages-browser)/./app/editor/components/AutoSave/AutoSave.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Import new advanced features\n\n\n\n\nfunction EditorPage() {\n    _s();\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHistory, setShowHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const bgColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.useColorModeValue)(\"gray.50\", \"gray.900\");\n    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.useColorModeValue)(\"gray.200\", \"gray.700\");\n    const { currentPage, selectedElement, selectedElements, isPreviewMode, currentBreakpoint } = (0,_lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_8__.useEditorStore)();\n    if (showPreview) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Preview_ResponsivePreview__WEBPACK_IMPORTED_MODULE_7__.ResponsivePreview, {\n            onClose: ()=>setShowPreview(false)\n        }, void 0, false, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n            lineNumber: 34,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Box, {\n        h: \"100vh\",\n        bg: bgColor,\n        overflow: \"hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Toolbar_EditorToolbar__WEBPACK_IMPORTED_MODULE_6__.EditorToolbar, {\n                onPreview: ()=>setShowPreview(true),\n                onToggleHistory: ()=>setShowHistory(!showHistory)\n            }, void 0, false, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Flex, {\n                h: \"calc(100vh - 60px)\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                        w: \"280px\",\n                        borderRight: \"1px\",\n                        borderColor: borderColor,\n                        bg: \"white\",\n                        overflowY: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Palette_ComponentPalette__WEBPACK_IMPORTED_MODULE_3__.ComponentPalette, {}, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Flex, {\n                        flex: \"1\",\n                        direction: \"column\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Canvas_DragDropCanvas__WEBPACK_IMPORTED_MODULE_2__.DragDropCanvas, {}, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Flex, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                w: \"320px\",\n                                borderLeft: \"1px\",\n                                borderColor: borderColor,\n                                bg: \"white\",\n                                overflowY: \"auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.Flex, {\n                                    direction: \"column\",\n                                    h: \"100%\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                            flex: \"1\",\n                                            borderBottom: \"1px\",\n                                            borderColor: borderColor,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layers_LayersPanel__WEBPACK_IMPORTED_MODULE_5__.LayersPanel, {}, void 0, false, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Box, {\n                                            flex: \"1\",\n                                            children: selectedElements.length > 1 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Properties_EnhancedElementProperties__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                elements: selectedElements\n                                            }, void 0, false, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 19\n                                            }, this) : selectedElement ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Properties_EnhancedElementProperties__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                element: selectedElement\n                                            }, void 0, false, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Properties_PropertyPanel__WEBPACK_IMPORTED_MODULE_4__.PropertyPanel, {}, void 0, false, {\n                                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, this),\n                            showHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_History_HistoryPanel__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 27\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_KeyboardShortcuts_KeyboardShortcuts__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AutoSave_AutoSave__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                enabled: true,\n                interval: 30000\n            }, void 0, false, {\n                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\page.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorPage, \"nuR8c5y18b2aDU9Q3orfbjrfl48=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.useColorModeValue,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_13__.useColorModeValue,\n        _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_8__.useEditorStore\n    ];\n});\n_c = EditorPage;\nvar _c;\n$RefreshReg$(_c, \"EditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/editor/page.tsx\n"));

/***/ })

});