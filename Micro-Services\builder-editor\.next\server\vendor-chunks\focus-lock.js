"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/focus-lock";
exports.ids = ["vendor-chunks/focus-lock"];
exports.modules = {

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/commands.js":
/*!*************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/commands.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusOn: () => (/* binding */ focusOn)\n/* harmony export */ });\nvar focusOn = function (target, focusOptions) {\n    if (!target) {\n        // not clear how, but is possible https://github.com/theKashey/focus-lock/issues/53\n        return;\n    }\n    if ('focus' in target) {\n        target.focus(focusOptions);\n    }\n    if ('contentWindow' in target && target.contentWindow) {\n        target.contentWindow.focus();\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvY29tbWFuZHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZXItZWRpdG9yLy4uLy4uL25vZGVfbW9kdWxlcy9mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L2NvbW1hbmRzLmpzPzI3NmQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHZhciBmb2N1c09uID0gZnVuY3Rpb24gKHRhcmdldCwgZm9jdXNPcHRpb25zKSB7XG4gICAgaWYgKCF0YXJnZXQpIHtcbiAgICAgICAgLy8gbm90IGNsZWFyIGhvdywgYnV0IGlzIHBvc3NpYmxlIGh0dHBzOi8vZ2l0aHViLmNvbS90aGVLYXNoZXkvZm9jdXMtbG9jay9pc3N1ZXMvNTNcbiAgICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBpZiAoJ2ZvY3VzJyBpbiB0YXJnZXQpIHtcbiAgICAgICAgdGFyZ2V0LmZvY3VzKGZvY3VzT3B0aW9ucyk7XG4gICAgfVxuICAgIGlmICgnY29udGVudFdpbmRvdycgaW4gdGFyZ2V0ICYmIHRhcmdldC5jb250ZW50V2luZG93KSB7XG4gICAgICAgIHRhcmdldC5jb250ZW50V2luZG93LmZvY3VzKCk7XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/commands.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/constants.js":
/*!**************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/constants.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FOCUS_ALLOW: () => (/* binding */ FOCUS_ALLOW),\n/* harmony export */   FOCUS_AUTO: () => (/* binding */ FOCUS_AUTO),\n/* harmony export */   FOCUS_DISABLED: () => (/* binding */ FOCUS_DISABLED),\n/* harmony export */   FOCUS_GROUP: () => (/* binding */ FOCUS_GROUP),\n/* harmony export */   FOCUS_NO_AUTOFOCUS: () => (/* binding */ FOCUS_NO_AUTOFOCUS)\n/* harmony export */ });\n/**\n * defines a focus group\n */\nvar FOCUS_GROUP = 'data-focus-lock';\n/**\n * disables element discovery inside a group marked by key\n */\nvar FOCUS_DISABLED = 'data-focus-lock-disabled';\n/**\n * allows uncontrolled focus within the marked area, effectively disabling focus lock for it's content\n */\nvar FOCUS_ALLOW = 'data-no-focus-lock';\n/**\n * instructs autofocus engine to pick default autofocus inside a given node\n * can be set on the element or container\n */\nvar FOCUS_AUTO = 'data-autofocus-inside';\n/**\n * instructs autofocus to ignore elements within a given node\n * can be set on the element or container\n */\nvar FOCUS_NO_AUTOFOCUS = 'data-no-autofocus';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvY29uc3RhbnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ08iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGVyLWVkaXRvci8uLi8uLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9jb25zdGFudHMuanM/MTU2NCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGRlZmluZXMgYSBmb2N1cyBncm91cFxuICovXG5leHBvcnQgdmFyIEZPQ1VTX0dST1VQID0gJ2RhdGEtZm9jdXMtbG9jayc7XG4vKipcbiAqIGRpc2FibGVzIGVsZW1lbnQgZGlzY292ZXJ5IGluc2lkZSBhIGdyb3VwIG1hcmtlZCBieSBrZXlcbiAqL1xuZXhwb3J0IHZhciBGT0NVU19ESVNBQkxFRCA9ICdkYXRhLWZvY3VzLWxvY2stZGlzYWJsZWQnO1xuLyoqXG4gKiBhbGxvd3MgdW5jb250cm9sbGVkIGZvY3VzIHdpdGhpbiB0aGUgbWFya2VkIGFyZWEsIGVmZmVjdGl2ZWx5IGRpc2FibGluZyBmb2N1cyBsb2NrIGZvciBpdCdzIGNvbnRlbnRcbiAqL1xuZXhwb3J0IHZhciBGT0NVU19BTExPVyA9ICdkYXRhLW5vLWZvY3VzLWxvY2snO1xuLyoqXG4gKiBpbnN0cnVjdHMgYXV0b2ZvY3VzIGVuZ2luZSB0byBwaWNrIGRlZmF1bHQgYXV0b2ZvY3VzIGluc2lkZSBhIGdpdmVuIG5vZGVcbiAqIGNhbiBiZSBzZXQgb24gdGhlIGVsZW1lbnQgb3IgY29udGFpbmVyXG4gKi9cbmV4cG9ydCB2YXIgRk9DVVNfQVVUTyA9ICdkYXRhLWF1dG9mb2N1cy1pbnNpZGUnO1xuLyoqXG4gKiBpbnN0cnVjdHMgYXV0b2ZvY3VzIHRvIGlnbm9yZSBlbGVtZW50cyB3aXRoaW4gYSBnaXZlbiBub2RlXG4gKiBjYW4gYmUgc2V0IG9uIHRoZSBlbGVtZW50IG9yIGNvbnRhaW5lclxuICovXG5leHBvcnQgdmFyIEZPQ1VTX05PX0FVVE9GT0NVUyA9ICdkYXRhLW5vLWF1dG9mb2N1cyc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/constants.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/focusInside.js":
/*!****************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/focusInside.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusInside: () => (/* binding */ focusInside)\n/* harmony export */ });\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/DOMutils */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_all_affected__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/all-affected */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/all-affected.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/array */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/getActiveElement */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\");\n\n\n\n\nvar focusInFrame = function (frame, activeElement) { return frame === activeElement; };\nvar focusInsideIframe = function (topNode, activeElement) {\n    return Boolean((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(topNode.querySelectorAll('iframe')).some(function (node) { return focusInFrame(node, activeElement); }));\n};\n/**\n * @returns {Boolean} true, if the current focus is inside given node or nodes.\n * Supports nodes hidden inside shadowDom\n */\nvar focusInside = function (topNode, activeElement) {\n    // const activeElement = document && getActiveElement();\n    if (activeElement === void 0) { activeElement = (0,_utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__.getActiveElement)((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.getFirst)(topNode).ownerDocument); }\n    if (!activeElement || (activeElement.dataset && activeElement.dataset.focusGuard)) {\n        return false;\n    }\n    return (0,_utils_all_affected__WEBPACK_IMPORTED_MODULE_2__.getAllAffectedNodes)(topNode).some(function (node) {\n        return (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__.contains)(node, activeElement) || focusInsideIframe(node, activeElement);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/focusInside.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/focusIsHidden.js":
/*!******************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/focusIsHidden.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusIsHidden: () => (/* binding */ focusIsHidden)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./constants */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/DOMutils */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/array */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _utils_getActiveElement__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/getActiveElement */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\");\n\n\n\n\n/**\n * checks if focus is hidden FROM the focus-lock\n * ie contained inside a node focus-lock shall ignore\n *\n * This is a utility function coupled with {@link FOCUS_ALLOW} constant\n *\n * @returns {boolean} focus is currently is in \"allow\" area\n */\nvar focusIsHidden = function (inDocument) {\n    if (inDocument === void 0) { inDocument = document; }\n    var activeElement = (0,_utils_getActiveElement__WEBPACK_IMPORTED_MODULE_0__.getActiveElement)(inDocument);\n    if (!activeElement) {\n        return false;\n    }\n    // this does not support setting FOCUS_ALLOW within shadow dom\n    return (0,_utils_array__WEBPACK_IMPORTED_MODULE_1__.toArray)(inDocument.querySelectorAll(\"[\".concat(_constants__WEBPACK_IMPORTED_MODULE_2__.FOCUS_ALLOW, \"]\"))).some(function (node) { return (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_3__.contains)(node, activeElement); });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvZm9jdXNJc0hpZGRlbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUEwQztBQUNFO0FBQ0o7QUFDb0I7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0Q0FBNEMsbUJBQW1CO0FBQy9EO0FBQ0EsYUFBYSxTQUFTO0FBQ3RCO0FBQ087QUFDUCxpQ0FBaUM7QUFDakMsd0JBQXdCLHlFQUFnQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcscURBQU8sd0NBQXdDLG1EQUFXLGdDQUFnQyxPQUFPLHlEQUFRLHdCQUF3QjtBQUM1SSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZXItZWRpdG9yLy4uLy4uL25vZGVfbW9kdWxlcy9mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L2ZvY3VzSXNIaWRkZW4uanM/MWQ5MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBGT0NVU19BTExPVyB9IGZyb20gJy4vY29uc3RhbnRzJztcbmltcG9ydCB7IGNvbnRhaW5zIH0gZnJvbSAnLi91dGlscy9ET011dGlscyc7XG5pbXBvcnQgeyB0b0FycmF5IH0gZnJvbSAnLi91dGlscy9hcnJheSc7XG5pbXBvcnQgeyBnZXRBY3RpdmVFbGVtZW50IH0gZnJvbSAnLi91dGlscy9nZXRBY3RpdmVFbGVtZW50Jztcbi8qKlxuICogY2hlY2tzIGlmIGZvY3VzIGlzIGhpZGRlbiBGUk9NIHRoZSBmb2N1cy1sb2NrXG4gKiBpZSBjb250YWluZWQgaW5zaWRlIGEgbm9kZSBmb2N1cy1sb2NrIHNoYWxsIGlnbm9yZVxuICpcbiAqIFRoaXMgaXMgYSB1dGlsaXR5IGZ1bmN0aW9uIGNvdXBsZWQgd2l0aCB7QGxpbmsgRk9DVVNfQUxMT1d9IGNvbnN0YW50XG4gKlxuICogQHJldHVybnMge2Jvb2xlYW59IGZvY3VzIGlzIGN1cnJlbnRseSBpcyBpbiBcImFsbG93XCIgYXJlYVxuICovXG5leHBvcnQgdmFyIGZvY3VzSXNIaWRkZW4gPSBmdW5jdGlvbiAoaW5Eb2N1bWVudCkge1xuICAgIGlmIChpbkRvY3VtZW50ID09PSB2b2lkIDApIHsgaW5Eb2N1bWVudCA9IGRvY3VtZW50OyB9XG4gICAgdmFyIGFjdGl2ZUVsZW1lbnQgPSBnZXRBY3RpdmVFbGVtZW50KGluRG9jdW1lbnQpO1xuICAgIGlmICghYWN0aXZlRWxlbWVudCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIC8vIHRoaXMgZG9lcyBub3Qgc3VwcG9ydCBzZXR0aW5nIEZPQ1VTX0FMTE9XIHdpdGhpbiBzaGFkb3cgZG9tXG4gICAgcmV0dXJuIHRvQXJyYXkoaW5Eb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKFwiW1wiLmNvbmNhdChGT0NVU19BTExPVywgXCJdXCIpKSkuc29tZShmdW5jdGlvbiAobm9kZSkgeyByZXR1cm4gY29udGFpbnMobm9kZSwgYWN0aXZlRWxlbWVudCk7IH0pO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/focusIsHidden.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/focusSolver.js":
/*!****************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/focusSolver.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusSolver: () => (/* binding */ focusSolver)\n/* harmony export */ });\n/* harmony import */ var _solver__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./solver */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/solver.js\");\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/DOMutils */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_all_affected__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/all-affected */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/all-affected.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/array */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _utils_auto_focus__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils/auto-focus */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/auto-focus.js\");\n/* harmony import */ var _utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/getActiveElement */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\");\n/* harmony import */ var _utils_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/is.js\");\n/* harmony import */ var _utils_parenting__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/parenting */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/parenting.js\");\n\n\n\n\n\n\n\n\nvar reorderNodes = function (srcNodes, dstNodes) {\n    var remap = new Map();\n    // no Set(dstNodes) for IE11 :(\n    dstNodes.forEach(function (entity) { return remap.set(entity.node, entity); });\n    // remap to dstNodes\n    return srcNodes.map(function (node) { return remap.get(node); }).filter(_utils_is__WEBPACK_IMPORTED_MODULE_0__.isDefined);\n};\n/**\n * contains the main logic of the `focus-lock` package.\n *\n * ! you probably dont need this function !\n *\n * given top node(s) and the last active element returns the element to be focused next\n * @returns element which should be focused to move focus inside\n * @param topNode\n * @param lastNode\n */\nvar focusSolver = function (topNode, lastNode) {\n    var activeElement = (0,_utils_getActiveElement__WEBPACK_IMPORTED_MODULE_1__.getActiveElement)((0,_utils_array__WEBPACK_IMPORTED_MODULE_2__.asArray)(topNode).length > 0 ? document : (0,_utils_array__WEBPACK_IMPORTED_MODULE_2__.getFirst)(topNode).ownerDocument);\n    var entries = (0,_utils_all_affected__WEBPACK_IMPORTED_MODULE_3__.getAllAffectedNodes)(topNode).filter(_utils_is__WEBPACK_IMPORTED_MODULE_0__.isNotAGuard);\n    var commonParent = (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_4__.getTopCommonParent)(activeElement || topNode, topNode, entries);\n    var visibilityCache = new Map();\n    var anyFocusable = (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_5__.getFocusableNodes)(entries, visibilityCache);\n    var innerElements = anyFocusable.filter(function (_a) {\n        var node = _a.node;\n        return (0,_utils_is__WEBPACK_IMPORTED_MODULE_0__.isNotAGuard)(node);\n    });\n    if (!innerElements[0]) {\n        return undefined;\n    }\n    var outerNodes = (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_5__.getFocusableNodes)([commonParent], visibilityCache).map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var orderedInnerElements = reorderNodes(outerNodes, innerElements);\n    // collect inner focusable and separately tabbables\n    var innerFocusables = orderedInnerElements.map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var innerTabbable = orderedInnerElements.filter(function (_a) {\n        var tabIndex = _a.tabIndex;\n        return tabIndex >= 0;\n    }).map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var newId = (0,_solver__WEBPACK_IMPORTED_MODULE_6__.newFocus)(innerFocusables, innerTabbable, outerNodes, activeElement, lastNode);\n    if (newId === _solver__WEBPACK_IMPORTED_MODULE_6__.NEW_FOCUS) {\n        var focusNode = \n        // first try only tabbable, and the fallback to all focusable, as long as at least one element should be picked for focus\n        (0,_utils_auto_focus__WEBPACK_IMPORTED_MODULE_7__.pickAutofocus)(anyFocusable, innerTabbable, (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_4__.allParentAutofocusables)(entries, visibilityCache)) ||\n            (0,_utils_auto_focus__WEBPACK_IMPORTED_MODULE_7__.pickAutofocus)(anyFocusable, innerFocusables, (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_4__.allParentAutofocusables)(entries, visibilityCache));\n        if (focusNode) {\n            return { node: focusNode };\n        }\n        else {\n            console.warn('focus-lock: cannot find any node to move focus into');\n            return undefined;\n        }\n    }\n    if (newId === undefined) {\n        return newId;\n    }\n    return orderedInnerElements[newId];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/focusSolver.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/focusables.js":
/*!***************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/focusables.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   expandFocusableNodes: () => (/* binding */ expandFocusableNodes)\n/* harmony export */ });\n/* harmony import */ var _utils_all_affected__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/all-affected */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/all-affected.js\");\n/* harmony import */ var _utils_is__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/is */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/is.js\");\n/* harmony import */ var _utils_parenting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/parenting */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/parenting.js\");\n/* harmony import */ var _utils_tabOrder__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/tabOrder */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabOrder.js\");\n/* harmony import */ var _utils_tabUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/tabUtils */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabUtils.js\");\n\n\n\n\n\n/**\n * traverses all related nodes (including groups) returning a list of all nodes(outer and internal) with meta information\n * This is low-level API!\n * @returns list of focusable elements inside a given top(!) node.\n * @see {@link getFocusableNodes} providing a simpler API\n */\nvar expandFocusableNodes = function (topNode) {\n    var entries = (0,_utils_all_affected__WEBPACK_IMPORTED_MODULE_0__.getAllAffectedNodes)(topNode).filter(_utils_is__WEBPACK_IMPORTED_MODULE_1__.isNotAGuard);\n    var commonParent = (0,_utils_parenting__WEBPACK_IMPORTED_MODULE_2__.getTopCommonParent)(topNode, topNode, entries);\n    var outerNodes = (0,_utils_tabOrder__WEBPACK_IMPORTED_MODULE_3__.orderByTabIndex)((0,_utils_tabUtils__WEBPACK_IMPORTED_MODULE_4__.getFocusables)([commonParent], true), true, true);\n    var innerElements = (0,_utils_tabUtils__WEBPACK_IMPORTED_MODULE_4__.getFocusables)(entries, false);\n    return outerNodes.map(function (_a) {\n        var node = _a.node, index = _a.index;\n        return ({\n            node: node,\n            index: index,\n            lockItem: innerElements.indexOf(node) >= 0,\n            guard: (0,_utils_is__WEBPACK_IMPORTED_MODULE_1__.isGuard)(node),\n        });\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/focusables.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/moveFocusInside.js":
/*!********************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/moveFocusInside.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   moveFocusInside: () => (/* binding */ moveFocusInside)\n/* harmony export */ });\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./commands */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/commands.js\");\n/* harmony import */ var _focusSolver__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./focusSolver */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/focusSolver.js\");\n\n\nvar guardCount = 0;\nvar lockDisabled = false;\n/**\n * The main functionality of the focus-lock package\n *\n * Contains focus at a given node.\n * The last focused element will help to determine which element(first or last) should be focused.\n * The found element will be focused.\n *\n * This is one time action (move), not a persistent focus-lock\n *\n * HTML markers (see {@link import('./constants').FOCUS_AUTO} constants) can control autofocus\n * @see {@link focusSolver} for the same functionality without autofocus\n */\nvar moveFocusInside = function (topNode, lastNode, options) {\n    if (options === void 0) { options = {}; }\n    var focusable = (0,_focusSolver__WEBPACK_IMPORTED_MODULE_0__.focusSolver)(topNode, lastNode);\n    // global local side effect to countain recursive lock activation and resolve focus-fighting\n    if (lockDisabled) {\n        return;\n    }\n    if (focusable) {\n        /** +FOCUS-FIGHTING prevention **/\n        if (guardCount > 2) {\n            // we have recursive entered back the lock activation\n            console.error('FocusLock: focus-fighting detected. Only one focus management system could be active. ' +\n                'See https://github.com/theKashey/focus-lock/#focus-fighting');\n            lockDisabled = true;\n            setTimeout(function () {\n                lockDisabled = false;\n            }, 1);\n            return;\n        }\n        guardCount++;\n        (0,_commands__WEBPACK_IMPORTED_MODULE_1__.focusOn)(focusable.node, options.focusOptions);\n        guardCount--;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/moveFocusInside.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/return-focus.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/return-focus.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   captureFocusRestore: () => (/* binding */ captureFocusRestore),\n/* harmony export */   recordElementLocation: () => (/* binding */ recordElementLocation)\n/* harmony export */ });\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/DOMutils */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n\nfunction weakRef(value) {\n    if (!value)\n        return null;\n    // #68 Safari 14.1 dont have it yet\n    // FIXME: remove in 2025\n    if (typeof WeakRef === 'undefined') {\n        return function () { return value || null; };\n    }\n    var w = value ? new WeakRef(value) : null;\n    return function () { return (w === null || w === void 0 ? void 0 : w.deref()) || null; };\n}\nvar recordElementLocation = function (element) {\n    if (!element) {\n        return null;\n    }\n    var stack = [];\n    var currentElement = element;\n    while (currentElement && currentElement !== document.body) {\n        stack.push({\n            current: weakRef(currentElement),\n            parent: weakRef(currentElement.parentElement),\n            left: weakRef(currentElement.previousElementSibling),\n            right: weakRef(currentElement.nextElementSibling),\n        });\n        currentElement = currentElement.parentElement;\n    }\n    return {\n        element: weakRef(element),\n        stack: stack,\n        ownerDocument: element.ownerDocument,\n    };\n};\nvar restoreFocusTo = function (location) {\n    var _a, _b, _c, _d, _e;\n    if (!location) {\n        return undefined;\n    }\n    var stack = location.stack, ownerDocument = location.ownerDocument;\n    var visibilityCache = new Map();\n    for (var _i = 0, stack_1 = stack; _i < stack_1.length; _i++) {\n        var line = stack_1[_i];\n        var parent_1 = (_a = line.parent) === null || _a === void 0 ? void 0 : _a.call(line);\n        // is it still here?\n        if (parent_1 && ownerDocument.contains(parent_1)) {\n            var left = (_b = line.left) === null || _b === void 0 ? void 0 : _b.call(line);\n            var savedCurrent = line.current();\n            var current = parent_1.contains(savedCurrent) ? savedCurrent : undefined;\n            var right = (_c = line.right) === null || _c === void 0 ? void 0 : _c.call(line);\n            var focusables = (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_0__.getTabbableNodes)([parent_1], visibilityCache);\n            var aim = \n            // that is element itself\n            (_e = (_d = current !== null && current !== void 0 ? current : \n            // or something in it's place\n            left === null || left === void 0 ? void 0 : left.nextElementSibling) !== null && _d !== void 0 ? _d : \n            // or somebody to the right, still close enough\n            right) !== null && _e !== void 0 ? _e : \n            // or somebody to the left, something?\n            left;\n            while (aim) {\n                for (var _f = 0, focusables_1 = focusables; _f < focusables_1.length; _f++) {\n                    var focusable = focusables_1[_f];\n                    if (aim === null || aim === void 0 ? void 0 : aim.contains(focusable.node)) {\n                        return focusable.node;\n                    }\n                }\n                aim = aim.nextElementSibling;\n            }\n            if (focusables.length) {\n                // if parent contains a focusable - move there\n                return focusables[0].node;\n            }\n        }\n    }\n    // nothing matched\n    return undefined;\n};\n/**\n * Captures the current focused element to restore focus as close as possible in the future\n * Handles situations where the focused element is removed from the DOM or no longer focusable\n * moving focus to the closest focusable element\n * @param targetElement - element where focus should be restored\n * @returns a function returning a new element to focus\n */\nvar captureFocusRestore = function (targetElement) {\n    var location = recordElementLocation(targetElement);\n    return function () {\n        return restoreFocusTo(location);\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/return-focus.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/sibling.js":
/*!************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/sibling.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusFirstElement: () => (/* binding */ focusFirstElement),\n/* harmony export */   focusLastElement: () => (/* binding */ focusLastElement),\n/* harmony export */   focusNextElement: () => (/* binding */ focusNextElement),\n/* harmony export */   focusPrevElement: () => (/* binding */ focusPrevElement),\n/* harmony export */   getRelativeFocusable: () => (/* binding */ getRelativeFocusable)\n/* harmony export */ });\n/* harmony import */ var _commands__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./commands */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/commands.js\");\n/* harmony import */ var _utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/DOMutils */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _utils_array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/array */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js\");\n\n\n\n/**\n * for a given `element` in a given `scope` returns focusable siblings\n * @param element - base element\n * @param scope - common parent. Can be document, but better to narrow it down for performance reasons\n * @returns {prev,next} - references to a focusable element before and after\n * @returns undefined - if operation is not applicable\n */\nvar getRelativeFocusable = function (element, scope, useTabbables) {\n    if (!element || !scope) {\n        console.error('no element or scope given');\n        return {};\n    }\n    var shards = (0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(scope);\n    if (shards.every(function (shard) { return !(0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.contains)(shard, element); })) {\n        console.error('Active element is not contained in the scope');\n        return {};\n    }\n    var focusables = useTabbables\n        ? (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getTabbableNodes)(shards, new Map())\n        : (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getFocusableNodes)(shards, new Map());\n    var current = focusables.findIndex(function (_a) {\n        var node = _a.node;\n        return node === element;\n    });\n    if (current === -1) {\n        // an edge case, when anchor element is not found\n        return undefined;\n    }\n    return {\n        prev: focusables[current - 1],\n        next: focusables[current + 1],\n        first: focusables[0],\n        last: focusables[focusables.length - 1],\n    };\n};\nvar getBoundary = function (shards, useTabbables) {\n    var set = useTabbables\n        ? (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getTabbableNodes)((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(shards), new Map())\n        : (0,_utils_DOMutils__WEBPACK_IMPORTED_MODULE_1__.getFocusableNodes)((0,_utils_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(shards), new Map());\n    return {\n        first: set[0],\n        last: set[set.length - 1],\n    };\n};\nvar defaultOptions = function (options) {\n    return Object.assign({\n        scope: document.body,\n        cycle: true,\n        onlyTabbable: true,\n    }, options);\n};\nvar moveFocus = function (fromElement, options, cb) {\n    if (options === void 0) { options = {}; }\n    var newOptions = defaultOptions(options);\n    var solution = getRelativeFocusable(fromElement, newOptions.scope, newOptions.onlyTabbable);\n    if (!solution) {\n        return;\n    }\n    var target = cb(solution, newOptions.cycle);\n    if (target) {\n        (0,_commands__WEBPACK_IMPORTED_MODULE_2__.focusOn)(target.node, newOptions.focusOptions);\n    }\n};\n/**\n * focuses next element in the tab-order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nvar focusNextElement = function (fromElement, options) {\n    if (options === void 0) { options = {}; }\n    moveFocus(fromElement, options, function (_a, cycle) {\n        var next = _a.next, first = _a.first;\n        return next || (cycle && first);\n    });\n};\n/**\n * focuses prev element in the tab order\n * @param fromElement - common parent to scope active element search or tab cycle order\n * @param {FocusNextOptions} [options] - focus options\n */\nvar focusPrevElement = function (fromElement, options) {\n    if (options === void 0) { options = {}; }\n    moveFocus(fromElement, options, function (_a, cycle) {\n        var prev = _a.prev, last = _a.last;\n        return prev || (cycle && last);\n    });\n};\nvar pickBoundary = function (scope, options, what) {\n    var _a;\n    var boundary = getBoundary(scope, (_a = options.onlyTabbable) !== null && _a !== void 0 ? _a : true);\n    var node = boundary[what];\n    if (node) {\n        (0,_commands__WEBPACK_IMPORTED_MODULE_2__.focusOn)(node.node, options.focusOptions);\n    }\n};\n/**\n * focuses first element in the tab-order\n * @param {FocusNextOptions} options - focus options\n */\nvar focusFirstElement = function (scope, options) {\n    if (options === void 0) { options = {}; }\n    pickBoundary(scope, options, 'first');\n};\n/**\n * focuses last element in the tab order\n * @param {FocusNextOptions} options - focus options\n */\nvar focusLastElement = function (scope, options) {\n    if (options === void 0) { options = {}; }\n    pickBoundary(scope, options, 'last');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/sibling.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/solver.js":
/*!***********************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/solver.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NEW_FOCUS: () => (/* binding */ NEW_FOCUS),\n/* harmony export */   newFocus: () => (/* binding */ newFocus)\n/* harmony export */ });\n/* harmony import */ var _utils_correctFocus__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/correctFocus */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/correctFocus.js\");\n/* harmony import */ var _utils_firstFocus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/firstFocus */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/firstFocus.js\");\n/* harmony import */ var _utils_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/is */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/is.js\");\n\n\n\nvar NEW_FOCUS = 'NEW_FOCUS';\n/**\n * Main solver for the \"find next focus\" question\n * @param innerNodes - used to control \"return focus\"\n * @param innerTabbables - used to control \"autofocus\"\n * @param outerNodes\n * @param activeElement\n * @param lastNode\n * @returns {number|string|undefined|*}\n */\nvar newFocus = function (innerNodes, innerTabbables, outerNodes, activeElement, lastNode) {\n    var cnt = innerNodes.length;\n    var firstFocus = innerNodes[0];\n    var lastFocus = innerNodes[cnt - 1];\n    var isOnGuard = (0,_utils_is__WEBPACK_IMPORTED_MODULE_0__.isGuard)(activeElement);\n    // focus is inside\n    if (activeElement && innerNodes.indexOf(activeElement) >= 0) {\n        return undefined;\n    }\n    var activeIndex = activeElement !== undefined ? outerNodes.indexOf(activeElement) : -1;\n    var lastIndex = lastNode ? outerNodes.indexOf(lastNode) : activeIndex;\n    var lastNodeInside = lastNode ? innerNodes.indexOf(lastNode) : -1;\n    // no active focus (or focus is on the body)\n    if (activeIndex === -1) {\n        // known fallback\n        if (lastNodeInside !== -1) {\n            return lastNodeInside;\n        }\n        return NEW_FOCUS;\n    }\n    // new focus, nothing to calculate\n    if (lastNodeInside === -1) {\n        return NEW_FOCUS;\n    }\n    var indexDiff = activeIndex - lastIndex;\n    var firstNodeIndex = outerNodes.indexOf(firstFocus);\n    var lastNodeIndex = outerNodes.indexOf(lastFocus);\n    var correctedNodes = (0,_utils_correctFocus__WEBPACK_IMPORTED_MODULE_1__.correctNodes)(outerNodes);\n    var currentFocusableIndex = activeElement !== undefined ? correctedNodes.indexOf(activeElement) : -1;\n    var previousFocusableIndex = lastNode ? correctedNodes.indexOf(lastNode) : currentFocusableIndex;\n    var tabbableNodes = correctedNodes.filter(function (node) { return node.tabIndex >= 0; });\n    var currentTabbableIndex = activeElement !== undefined ? tabbableNodes.indexOf(activeElement) : -1;\n    var previousTabbableIndex = lastNode ? tabbableNodes.indexOf(lastNode) : currentTabbableIndex;\n    var focusIndexDiff = currentTabbableIndex >= 0 && previousTabbableIndex >= 0\n        ? // old/new are tabbables, measure distance in tabbable space\n            previousTabbableIndex - currentTabbableIndex\n        : // or else measure in focusable space\n            previousFocusableIndex - currentFocusableIndex;\n    // old focus\n    if (!indexDiff && lastNodeInside >= 0) {\n        return lastNodeInside;\n    }\n    // no tabbable elements, autofocus is not possible\n    if (innerTabbables.length === 0) {\n        // an edge case with no tabbable elements\n        // return the last focusable one\n        // with some probability this will prevent focus from cycling across the lock, but there is no tabbale elements to cycle to\n        return lastNodeInside;\n    }\n    var returnFirstNode = (0,_utils_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFocusable)(innerNodes, innerTabbables[0]);\n    var returnLastNode = (0,_utils_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFocusable)(innerNodes, innerTabbables[innerTabbables.length - 1]);\n    // first element\n    if (activeIndex <= firstNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n        return returnLastNode;\n    }\n    // last element\n    if (activeIndex >= lastNodeIndex && isOnGuard && Math.abs(indexDiff) > 1) {\n        return returnFirstNode;\n    }\n    // jump out, but not on the guard\n    if (indexDiff && Math.abs(focusIndexDiff) > 1) {\n        return lastNodeInside;\n    }\n    // focus above lock\n    if (activeIndex <= firstNodeIndex) {\n        return returnLastNode;\n    }\n    // focus below lock\n    if (activeIndex > lastNodeIndex) {\n        return returnFirstNode;\n    }\n    // index is inside tab order, but outside Lock\n    if (indexDiff) {\n        if (Math.abs(indexDiff) > 1) {\n            return lastNodeInside;\n        }\n        return (cnt + lastNodeInside + indexDiff) % cnt;\n    }\n    // do nothing\n    return undefined;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/solver.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contains: () => (/* binding */ contains),\n/* harmony export */   filterAutoFocusable: () => (/* binding */ filterAutoFocusable),\n/* harmony export */   filterFocusable: () => (/* binding */ filterFocusable),\n/* harmony export */   getFocusableNodes: () => (/* binding */ getFocusableNodes),\n/* harmony export */   getTabbableNodes: () => (/* binding */ getTabbableNodes),\n/* harmony export */   parentAutofocusables: () => (/* binding */ parentAutofocusables)\n/* harmony export */ });\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _is__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./is */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/is.js\");\n/* harmony import */ var _tabOrder__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tabOrder */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabOrder.js\");\n/* harmony import */ var _tabUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tabUtils */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabUtils.js\");\n\n\n\n\n/**\n * given list of focusable elements keeps the ones user can interact with\n * @param nodes\n * @param visibilityCache\n */\nvar filterFocusable = function (nodes, visibilityCache) {\n    return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(nodes)\n        .filter(function (node) { return (0,_is__WEBPACK_IMPORTED_MODULE_1__.isVisibleCached)(visibilityCache, node); })\n        .filter(function (node) { return (0,_is__WEBPACK_IMPORTED_MODULE_1__.notHiddenInput)(node); });\n};\nvar filterAutoFocusable = function (nodes, cache) {\n    if (cache === void 0) { cache = new Map(); }\n    return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(nodes).filter(function (node) { return (0,_is__WEBPACK_IMPORTED_MODULE_1__.isAutoFocusAllowedCached)(cache, node); });\n};\n/**\n * !__WARNING__! Low level API.\n * @returns all tabbable nodes\n *\n * @see {@link getFocusableNodes} to get any focusable element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nvar getTabbableNodes = function (topNodes, visibilityCache, withGuards) {\n    return (0,_tabOrder__WEBPACK_IMPORTED_MODULE_2__.orderByTabIndex)(filterFocusable((0,_tabUtils__WEBPACK_IMPORTED_MODULE_3__.getFocusables)(topNodes, withGuards), visibilityCache), true, withGuards);\n};\n/**\n * !__WARNING__! Low level API.\n *\n * @returns anything \"focusable\", not only tabbable. The difference is in `tabIndex=-1`\n * (without guards, as long as they are not expected to be ever focused)\n *\n * @see {@link getTabbableNodes} to get only tabble nodes element\n *\n * @param topNodes - array of top level HTMLElements to search inside\n * @param visibilityCache - an cache to store intermediate measurements. Expected to be a fresh `new Map` on every call\n */\nvar getFocusableNodes = function (topNodes, visibilityCache) {\n    return (0,_tabOrder__WEBPACK_IMPORTED_MODULE_2__.orderByTabIndex)(filterFocusable((0,_tabUtils__WEBPACK_IMPORTED_MODULE_3__.getFocusables)(topNodes), visibilityCache), false);\n};\n/**\n * return list of nodes which are expected to be auto-focused\n * @param topNode\n * @param visibilityCache\n */\nvar parentAutofocusables = function (topNode, visibilityCache) {\n    return filterFocusable((0,_tabUtils__WEBPACK_IMPORTED_MODULE_3__.getParentAutofocusables)(topNode), visibilityCache);\n};\n/*\n * Determines if element is contained in scope, including nested shadow DOMs\n */\nvar contains = function (scope, element) {\n    if (scope.shadowRoot) {\n        return contains(scope.shadowRoot, element);\n    }\n    else {\n        if (Object.getPrototypeOf(scope).contains !== undefined &&\n            Object.getPrototypeOf(scope).contains.call(scope, element)) {\n            return true;\n        }\n        return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(scope.children).some(function (child) {\n            var _a;\n            if (child instanceof HTMLIFrameElement) {\n                var iframeBody = (_a = child.contentDocument) === null || _a === void 0 ? void 0 : _a.body;\n                if (iframeBody) {\n                    return contains(iframeBody, element);\n                }\n                return false;\n            }\n            return contains(child, element);\n        });\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/all-affected.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/all-affected.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAllAffectedNodes: () => (/* binding */ getAllAffectedNodes)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js\");\n\n\n/**\n * in case of multiple nodes nested inside each other\n * keeps only top ones\n * this is O(nlogn)\n * @param nodes\n * @returns {*}\n */\nvar filterNested = function (nodes) {\n    var contained = new Set();\n    var l = nodes.length;\n    for (var i = 0; i < l; i += 1) {\n        for (var j = i + 1; j < l; j += 1) {\n            var position = nodes[i].compareDocumentPosition(nodes[j]);\n            /* eslint-disable no-bitwise */\n            if ((position & Node.DOCUMENT_POSITION_CONTAINED_BY) > 0) {\n                contained.add(j);\n            }\n            if ((position & Node.DOCUMENT_POSITION_CONTAINS) > 0) {\n                contained.add(i);\n            }\n            /* eslint-enable */\n        }\n    }\n    return nodes.filter(function (_, index) { return !contained.has(index); });\n};\n/**\n * finds top most parent for a node\n * @param node\n * @returns {*}\n */\nvar getTopParent = function (node) {\n    return node.parentNode ? getTopParent(node.parentNode) : node;\n};\n/**\n * returns all \"focus containers\" inside a given node\n * @param node - node or nodes to look inside\n * @returns Element[]\n */\nvar getAllAffectedNodes = function (node) {\n    var nodes = (0,_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(node);\n    return nodes.filter(Boolean).reduce(function (acc, currentNode) {\n        var group = currentNode.getAttribute(_constants__WEBPACK_IMPORTED_MODULE_1__.FOCUS_GROUP);\n        acc.push.apply(acc, (group\n            ? filterNested((0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(getTopParent(currentNode).querySelectorAll(\"[\".concat(_constants__WEBPACK_IMPORTED_MODULE_1__.FOCUS_GROUP, \"=\\\"\").concat(group, \"\\\"]:not([\").concat(_constants__WEBPACK_IMPORTED_MODULE_1__.FOCUS_DISABLED, \"=\\\"disabled\\\"])\"))))\n            : [currentNode]));\n        return acc;\n    }, []);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/all-affected.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js":
/*!****************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/array.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asArray: () => (/* binding */ asArray),\n/* harmony export */   getFirst: () => (/* binding */ getFirst),\n/* harmony export */   toArray: () => (/* binding */ toArray)\n/* harmony export */ });\n/*\nIE11 support\n */\nvar toArray = function (a) {\n    var ret = Array(a.length);\n    for (var i = 0; i < a.length; ++i) {\n        ret[i] = a[i];\n    }\n    return ret;\n};\nvar asArray = function (a) { return (Array.isArray(a) ? a : [a]); };\nvar getFirst = function (a) { return (Array.isArray(a) ? a[0] : a); };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvYXJyYXkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLG9CQUFvQixjQUFjO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ08sNkJBQTZCO0FBQzdCLDhCQUE4QiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZXItZWRpdG9yLy4uLy4uL25vZGVfbW9kdWxlcy9mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L3V0aWxzL2FycmF5LmpzPzQxOGQiXSwic291cmNlc0NvbnRlbnQiOlsiLypcbklFMTEgc3VwcG9ydFxuICovXG5leHBvcnQgdmFyIHRvQXJyYXkgPSBmdW5jdGlvbiAoYSkge1xuICAgIHZhciByZXQgPSBBcnJheShhLmxlbmd0aCk7XG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBhLmxlbmd0aDsgKytpKSB7XG4gICAgICAgIHJldFtpXSA9IGFbaV07XG4gICAgfVxuICAgIHJldHVybiByZXQ7XG59O1xuZXhwb3J0IHZhciBhc0FycmF5ID0gZnVuY3Rpb24gKGEpIHsgcmV0dXJuIChBcnJheS5pc0FycmF5KGEpID8gYSA6IFthXSk7IH07XG5leHBvcnQgdmFyIGdldEZpcnN0ID0gZnVuY3Rpb24gKGEpIHsgcmV0dXJuIChBcnJheS5pc0FycmF5KGEpID8gYVswXSA6IGEpOyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/auto-focus.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/auto-focus.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pickAutofocus: () => (/* binding */ pickAutofocus)\n/* harmony export */ });\n/* harmony import */ var _DOMutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DOMutils */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _firstFocus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./firstFocus */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/firstFocus.js\");\n/* harmony import */ var _is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/is.js\");\n\n\n\nvar findAutoFocused = function (autoFocusables) {\n    return function (node) {\n        var _a;\n        var autofocus = (_a = (0,_is__WEBPACK_IMPORTED_MODULE_0__.getDataset)(node)) === null || _a === void 0 ? void 0 : _a.autofocus;\n        return (\n        // @ts-expect-error\n        node.autofocus ||\n            //\n            (autofocus !== undefined && autofocus !== 'false') ||\n            //\n            autoFocusables.indexOf(node) >= 0);\n    };\n};\nvar pickAutofocus = function (nodesIndexes, orderedNodes, groups) {\n    var nodes = nodesIndexes.map(function (_a) {\n        var node = _a.node;\n        return node;\n    });\n    var autoFocusable = (0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.filterAutoFocusable)(nodes.filter(findAutoFocused(groups)));\n    if (autoFocusable && autoFocusable.length) {\n        return (0,_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFirstFocus)(autoFocusable);\n    }\n    return (0,_firstFocus__WEBPACK_IMPORTED_MODULE_2__.pickFirstFocus)((0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.filterAutoFocusable)(orderedNodes));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvYXV0by1mb2N1cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWlEO0FBQ0g7QUFDWjtBQUNsQztBQUNBO0FBQ0E7QUFDQSw4QkFBOEIsK0NBQVU7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsd0JBQXdCLDhEQUFtQjtBQUMzQztBQUNBLGVBQWUsMkRBQWM7QUFDN0I7QUFDQSxXQUFXLDJEQUFjLENBQUMsOERBQW1CO0FBQzdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRlci1lZGl0b3IvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvYXV0by1mb2N1cy5qcz8xNDEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZpbHRlckF1dG9Gb2N1c2FibGUgfSBmcm9tICcuL0RPTXV0aWxzJztcbmltcG9ydCB7IHBpY2tGaXJzdEZvY3VzIH0gZnJvbSAnLi9maXJzdEZvY3VzJztcbmltcG9ydCB7IGdldERhdGFzZXQgfSBmcm9tICcuL2lzJztcbnZhciBmaW5kQXV0b0ZvY3VzZWQgPSBmdW5jdGlvbiAoYXV0b0ZvY3VzYWJsZXMpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKG5vZGUpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICB2YXIgYXV0b2ZvY3VzID0gKF9hID0gZ2V0RGF0YXNldChub2RlKSkgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmF1dG9mb2N1cztcbiAgICAgICAgcmV0dXJuIChcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvclxuICAgICAgICBub2RlLmF1dG9mb2N1cyB8fFxuICAgICAgICAgICAgLy9cbiAgICAgICAgICAgIChhdXRvZm9jdXMgIT09IHVuZGVmaW5lZCAmJiBhdXRvZm9jdXMgIT09ICdmYWxzZScpIHx8XG4gICAgICAgICAgICAvL1xuICAgICAgICAgICAgYXV0b0ZvY3VzYWJsZXMuaW5kZXhPZihub2RlKSA+PSAwKTtcbiAgICB9O1xufTtcbmV4cG9ydCB2YXIgcGlja0F1dG9mb2N1cyA9IGZ1bmN0aW9uIChub2Rlc0luZGV4ZXMsIG9yZGVyZWROb2RlcywgZ3JvdXBzKSB7XG4gICAgdmFyIG5vZGVzID0gbm9kZXNJbmRleGVzLm1hcChmdW5jdGlvbiAoX2EpIHtcbiAgICAgICAgdmFyIG5vZGUgPSBfYS5ub2RlO1xuICAgICAgICByZXR1cm4gbm9kZTtcbiAgICB9KTtcbiAgICB2YXIgYXV0b0ZvY3VzYWJsZSA9IGZpbHRlckF1dG9Gb2N1c2FibGUobm9kZXMuZmlsdGVyKGZpbmRBdXRvRm9jdXNlZChncm91cHMpKSk7XG4gICAgaWYgKGF1dG9Gb2N1c2FibGUgJiYgYXV0b0ZvY3VzYWJsZS5sZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuIHBpY2tGaXJzdEZvY3VzKGF1dG9Gb2N1c2FibGUpO1xuICAgIH1cbiAgICByZXR1cm4gcGlja0ZpcnN0Rm9jdXMoZmlsdGVyQXV0b0ZvY3VzYWJsZShvcmRlcmVkTm9kZXMpKTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/auto-focus.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/correctFocus.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/correctFocus.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   correctNode: () => (/* binding */ correctNode),\n/* harmony export */   correctNodes: () => (/* binding */ correctNodes)\n/* harmony export */ });\n/* harmony import */ var _is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./is */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/is.js\");\n\nvar findSelectedRadio = function (node, nodes) {\n    return nodes\n        .filter(_is__WEBPACK_IMPORTED_MODULE_0__.isRadioElement)\n        .filter(function (el) { return el.name === node.name; })\n        .filter(function (el) { return el.checked; })[0] || node;\n};\nvar correctNode = function (node, nodes) {\n    if ((0,_is__WEBPACK_IMPORTED_MODULE_0__.isRadioElement)(node) && node.name) {\n        return findSelectedRadio(node, nodes);\n    }\n    return node;\n};\n/**\n * giving a set of radio inputs keeps only selected (tabbable) ones\n * @param nodes\n */\nvar correctNodes = function (nodes) {\n    // IE11 has no Set(array) constructor\n    var resultSet = new Set();\n    nodes.forEach(function (node) { return resultSet.add(correctNode(node, nodes)); });\n    // using filter to support IE11\n    return nodes.filter(function (node) { return resultSet.has(node); });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvY29ycmVjdEZvY3VzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFzQztBQUN0QztBQUNBO0FBQ0EsZ0JBQWdCLCtDQUFjO0FBQzlCLGdDQUFnQywrQkFBK0I7QUFDL0QsZ0NBQWdDLG9CQUFvQjtBQUNwRDtBQUNPO0FBQ1AsUUFBUSxtREFBYztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0Esb0NBQW9DLGlEQUFpRDtBQUNyRjtBQUNBLDBDQUEwQyw2QkFBNkI7QUFDdkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGVyLWVkaXRvci8uLi8uLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9jb3JyZWN0Rm9jdXMuanM/ZGI1MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc1JhZGlvRWxlbWVudCB9IGZyb20gJy4vaXMnO1xudmFyIGZpbmRTZWxlY3RlZFJhZGlvID0gZnVuY3Rpb24gKG5vZGUsIG5vZGVzKSB7XG4gICAgcmV0dXJuIG5vZGVzXG4gICAgICAgIC5maWx0ZXIoaXNSYWRpb0VsZW1lbnQpXG4gICAgICAgIC5maWx0ZXIoZnVuY3Rpb24gKGVsKSB7IHJldHVybiBlbC5uYW1lID09PSBub2RlLm5hbWU7IH0pXG4gICAgICAgIC5maWx0ZXIoZnVuY3Rpb24gKGVsKSB7IHJldHVybiBlbC5jaGVja2VkOyB9KVswXSB8fCBub2RlO1xufTtcbmV4cG9ydCB2YXIgY29ycmVjdE5vZGUgPSBmdW5jdGlvbiAobm9kZSwgbm9kZXMpIHtcbiAgICBpZiAoaXNSYWRpb0VsZW1lbnQobm9kZSkgJiYgbm9kZS5uYW1lKSB7XG4gICAgICAgIHJldHVybiBmaW5kU2VsZWN0ZWRSYWRpbyhub2RlLCBub2Rlcyk7XG4gICAgfVxuICAgIHJldHVybiBub2RlO1xufTtcbi8qKlxuICogZ2l2aW5nIGEgc2V0IG9mIHJhZGlvIGlucHV0cyBrZWVwcyBvbmx5IHNlbGVjdGVkICh0YWJiYWJsZSkgb25lc1xuICogQHBhcmFtIG5vZGVzXG4gKi9cbmV4cG9ydCB2YXIgY29ycmVjdE5vZGVzID0gZnVuY3Rpb24gKG5vZGVzKSB7XG4gICAgLy8gSUUxMSBoYXMgbm8gU2V0KGFycmF5KSBjb25zdHJ1Y3RvclxuICAgIHZhciByZXN1bHRTZXQgPSBuZXcgU2V0KCk7XG4gICAgbm9kZXMuZm9yRWFjaChmdW5jdGlvbiAobm9kZSkgeyByZXR1cm4gcmVzdWx0U2V0LmFkZChjb3JyZWN0Tm9kZShub2RlLCBub2RlcykpOyB9KTtcbiAgICAvLyB1c2luZyBmaWx0ZXIgdG8gc3VwcG9ydCBJRTExXG4gICAgcmV0dXJuIG5vZGVzLmZpbHRlcihmdW5jdGlvbiAobm9kZSkgeyByZXR1cm4gcmVzdWx0U2V0Lmhhcyhub2RlKTsgfSk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/correctFocus.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/firstFocus.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/firstFocus.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pickFirstFocus: () => (/* binding */ pickFirstFocus),\n/* harmony export */   pickFocusable: () => (/* binding */ pickFocusable)\n/* harmony export */ });\n/* harmony import */ var _correctFocus__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./correctFocus */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/correctFocus.js\");\n\nvar pickFirstFocus = function (nodes) {\n    if (nodes[0] && nodes.length > 1) {\n        return (0,_correctFocus__WEBPACK_IMPORTED_MODULE_0__.correctNode)(nodes[0], nodes);\n    }\n    return nodes[0];\n};\nvar pickFocusable = function (nodes, node) {\n    return nodes.indexOf((0,_correctFocus__WEBPACK_IMPORTED_MODULE_0__.correctNode)(node, nodes));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvZmlyc3RGb2N1cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkM7QUFDdEM7QUFDUDtBQUNBLGVBQWUsMERBQVc7QUFDMUI7QUFDQTtBQUNBO0FBQ087QUFDUCx5QkFBeUIsMERBQVc7QUFDcEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGVyLWVkaXRvci8uLi8uLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9maXJzdEZvY3VzLmpzPzY4MjAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY29ycmVjdE5vZGUgfSBmcm9tICcuL2NvcnJlY3RGb2N1cyc7XG5leHBvcnQgdmFyIHBpY2tGaXJzdEZvY3VzID0gZnVuY3Rpb24gKG5vZGVzKSB7XG4gICAgaWYgKG5vZGVzWzBdICYmIG5vZGVzLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgcmV0dXJuIGNvcnJlY3ROb2RlKG5vZGVzWzBdLCBub2Rlcyk7XG4gICAgfVxuICAgIHJldHVybiBub2Rlc1swXTtcbn07XG5leHBvcnQgdmFyIHBpY2tGb2N1c2FibGUgPSBmdW5jdGlvbiAobm9kZXMsIG5vZGUpIHtcbiAgICByZXR1cm4gbm9kZXMuaW5kZXhPZihjb3JyZWN0Tm9kZShub2RlLCBub2RlcykpO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/firstFocus.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/getActiveElement.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/getActiveElement.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getActiveElement: () => (/* binding */ getActiveElement)\n/* harmony export */ });\n/* harmony import */ var _safe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./safe */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/safe.js\");\n/**\n * returns active element from document or from nested shadowdoms\n */\n\n/**\n * returns current active element. If the active element is a \"container\" itself(shadowRoot or iframe) returns active element inside it\n * @param [inDocument]\n */\nvar getActiveElement = function (inDocument) {\n    if (inDocument === void 0) { inDocument = document; }\n    if (!inDocument || !inDocument.activeElement) {\n        return undefined;\n    }\n    var activeElement = inDocument.activeElement;\n    return (activeElement.shadowRoot\n        ? getActiveElement(activeElement.shadowRoot)\n        : activeElement instanceof HTMLIFrameElement && (0,_safe__WEBPACK_IMPORTED_MODULE_0__.safeProbe)(function () { return activeElement.contentWindow.document; })\n            ? getActiveElement(activeElement.contentWindow.document)\n            : activeElement);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvZ2V0QWN0aXZlRWxlbWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNtQztBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdEQUF3RCxnREFBUyxlQUFlLDhDQUE4QztBQUM5SDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGVyLWVkaXRvci8uLi8uLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy9nZXRBY3RpdmVFbGVtZW50LmpzPzhhZjgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiByZXR1cm5zIGFjdGl2ZSBlbGVtZW50IGZyb20gZG9jdW1lbnQgb3IgZnJvbSBuZXN0ZWQgc2hhZG93ZG9tc1xuICovXG5pbXBvcnQgeyBzYWZlUHJvYmUgfSBmcm9tICcuL3NhZmUnO1xuLyoqXG4gKiByZXR1cm5zIGN1cnJlbnQgYWN0aXZlIGVsZW1lbnQuIElmIHRoZSBhY3RpdmUgZWxlbWVudCBpcyBhIFwiY29udGFpbmVyXCIgaXRzZWxmKHNoYWRvd1Jvb3Qgb3IgaWZyYW1lKSByZXR1cm5zIGFjdGl2ZSBlbGVtZW50IGluc2lkZSBpdFxuICogQHBhcmFtIFtpbkRvY3VtZW50XVxuICovXG5leHBvcnQgdmFyIGdldEFjdGl2ZUVsZW1lbnQgPSBmdW5jdGlvbiAoaW5Eb2N1bWVudCkge1xuICAgIGlmIChpbkRvY3VtZW50ID09PSB2b2lkIDApIHsgaW5Eb2N1bWVudCA9IGRvY3VtZW50OyB9XG4gICAgaWYgKCFpbkRvY3VtZW50IHx8ICFpbkRvY3VtZW50LmFjdGl2ZUVsZW1lbnQpIHtcbiAgICAgICAgcmV0dXJuIHVuZGVmaW5lZDtcbiAgICB9XG4gICAgdmFyIGFjdGl2ZUVsZW1lbnQgPSBpbkRvY3VtZW50LmFjdGl2ZUVsZW1lbnQ7XG4gICAgcmV0dXJuIChhY3RpdmVFbGVtZW50LnNoYWRvd1Jvb3RcbiAgICAgICAgPyBnZXRBY3RpdmVFbGVtZW50KGFjdGl2ZUVsZW1lbnQuc2hhZG93Um9vdClcbiAgICAgICAgOiBhY3RpdmVFbGVtZW50IGluc3RhbmNlb2YgSFRNTElGcmFtZUVsZW1lbnQgJiYgc2FmZVByb2JlKGZ1bmN0aW9uICgpIHsgcmV0dXJuIGFjdGl2ZUVsZW1lbnQuY29udGVudFdpbmRvdy5kb2N1bWVudDsgfSlcbiAgICAgICAgICAgID8gZ2V0QWN0aXZlRWxlbWVudChhY3RpdmVFbGVtZW50LmNvbnRlbnRXaW5kb3cuZG9jdW1lbnQpXG4gICAgICAgICAgICA6IGFjdGl2ZUVsZW1lbnQpO1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/getActiveElement.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/is.js":
/*!*************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/is.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDataset: () => (/* binding */ getDataset),\n/* harmony export */   isAutoFocusAllowed: () => (/* binding */ isAutoFocusAllowed),\n/* harmony export */   isAutoFocusAllowedCached: () => (/* binding */ isAutoFocusAllowedCached),\n/* harmony export */   isDefined: () => (/* binding */ isDefined),\n/* harmony export */   isGuard: () => (/* binding */ isGuard),\n/* harmony export */   isHTMLButtonElement: () => (/* binding */ isHTMLButtonElement),\n/* harmony export */   isHTMLInputElement: () => (/* binding */ isHTMLInputElement),\n/* harmony export */   isNotAGuard: () => (/* binding */ isNotAGuard),\n/* harmony export */   isRadioElement: () => (/* binding */ isRadioElement),\n/* harmony export */   isVisibleCached: () => (/* binding */ isVisibleCached),\n/* harmony export */   notHiddenInput: () => (/* binding */ notHiddenInput)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/constants.js\");\n\nvar isElementHidden = function (node) {\n    // we can measure only \"elements\"\n    // consider others as \"visible\"\n    if (node.nodeType !== Node.ELEMENT_NODE) {\n        return false;\n    }\n    var computedStyle = window.getComputedStyle(node, null);\n    if (!computedStyle || !computedStyle.getPropertyValue) {\n        return false;\n    }\n    return (computedStyle.getPropertyValue('display') === 'none' || computedStyle.getPropertyValue('visibility') === 'hidden');\n};\nvar getParentNode = function (node) {\n    // DOCUMENT_FRAGMENT_NODE can also point on ShadowRoot. In this case .host will point on the next node\n    return node.parentNode && node.parentNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE\n        ? // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            node.parentNode.host\n        : node.parentNode;\n};\nvar isTopNode = function (node) {\n    // @ts-ignore\n    return node === document || (node && node.nodeType === Node.DOCUMENT_NODE);\n};\nvar isInert = function (node) { return node.hasAttribute('inert'); };\n/**\n * @see https://github.com/testing-library/jest-dom/blob/main/src/to-be-visible.js\n */\nvar isVisibleUncached = function (node, checkParent) {\n    return !node || isTopNode(node) || (!isElementHidden(node) && !isInert(node) && checkParent(getParentNode(node)));\n};\nvar isVisibleCached = function (visibilityCache, node) {\n    var cached = visibilityCache.get(node);\n    if (cached !== undefined) {\n        return cached;\n    }\n    var result = isVisibleUncached(node, isVisibleCached.bind(undefined, visibilityCache));\n    visibilityCache.set(node, result);\n    return result;\n};\nvar isAutoFocusAllowedUncached = function (node, checkParent) {\n    return node && !isTopNode(node) ? (isAutoFocusAllowed(node) ? checkParent(getParentNode(node)) : false) : true;\n};\nvar isAutoFocusAllowedCached = function (cache, node) {\n    var cached = cache.get(node);\n    if (cached !== undefined) {\n        return cached;\n    }\n    var result = isAutoFocusAllowedUncached(node, isAutoFocusAllowedCached.bind(undefined, cache));\n    cache.set(node, result);\n    return result;\n};\nvar getDataset = function (node) {\n    // @ts-ignore\n    return node.dataset;\n};\nvar isHTMLButtonElement = function (node) { return node.tagName === 'BUTTON'; };\nvar isHTMLInputElement = function (node) { return node.tagName === 'INPUT'; };\nvar isRadioElement = function (node) {\n    return isHTMLInputElement(node) && node.type === 'radio';\n};\nvar notHiddenInput = function (node) {\n    return !((isHTMLInputElement(node) || isHTMLButtonElement(node)) && (node.type === 'hidden' || node.disabled));\n};\nvar isAutoFocusAllowed = function (node) {\n    var attribute = node.getAttribute(_constants__WEBPACK_IMPORTED_MODULE_0__.FOCUS_NO_AUTOFOCUS);\n    return ![true, 'true', ''].includes(attribute);\n};\nvar isGuard = function (node) { var _a; return Boolean(node && ((_a = getDataset(node)) === null || _a === void 0 ? void 0 : _a.focusGuard)); };\nvar isNotAGuard = function (node) { return !isGuard(node); };\nvar isDefined = function (x) { return Boolean(x); };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/is.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/parenting.js":
/*!********************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/parenting.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   allParentAutofocusables: () => (/* binding */ allParentAutofocusables),\n/* harmony export */   getCommonParent: () => (/* binding */ getCommonParent),\n/* harmony export */   getTopCommonParent: () => (/* binding */ getTopCommonParent)\n/* harmony export */ });\n/* harmony import */ var _DOMutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DOMutils */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js\");\n\n\n\nvar getParents = function (node, parents) {\n    if (parents === void 0) { parents = []; }\n    parents.push(node);\n    if (node.parentNode) {\n        getParents(node.parentNode.host || node.parentNode, parents);\n    }\n    return parents;\n};\n/**\n * finds a parent for both nodeA and nodeB\n * @param nodeA\n * @param nodeB\n * @returns {boolean|*}\n */\nvar getCommonParent = function (nodeA, nodeB) {\n    var parentsA = getParents(nodeA);\n    var parentsB = getParents(nodeB);\n    // tslint:disable-next-line:prefer-for-of\n    for (var i = 0; i < parentsA.length; i += 1) {\n        var currentParent = parentsA[i];\n        if (parentsB.indexOf(currentParent) >= 0) {\n            return currentParent;\n        }\n    }\n    return false;\n};\nvar getTopCommonParent = function (baseActiveElement, leftEntry, rightEntries) {\n    var activeElements = (0,_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(baseActiveElement);\n    var leftEntries = (0,_array__WEBPACK_IMPORTED_MODULE_0__.asArray)(leftEntry);\n    var activeElement = activeElements[0];\n    var topCommon = false;\n    leftEntries.filter(Boolean).forEach(function (entry) {\n        topCommon = getCommonParent(topCommon || entry, entry) || topCommon;\n        rightEntries.filter(Boolean).forEach(function (subEntry) {\n            var common = getCommonParent(activeElement, subEntry);\n            if (common) {\n                if (!topCommon || (0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.contains)(common, topCommon)) {\n                    topCommon = common;\n                }\n                else {\n                    topCommon = getCommonParent(common, topCommon);\n                }\n            }\n        });\n    });\n    // TODO: add assert here?\n    return topCommon;\n};\n/**\n * return list of nodes which are expected to be autofocused inside a given top nodes\n * @param entries\n * @param visibilityCache\n */\nvar allParentAutofocusables = function (entries, visibilityCache) {\n    return entries.reduce(function (acc, node) { return acc.concat((0,_DOMutils__WEBPACK_IMPORTED_MODULE_1__.parentAutofocusables)(node, visibilityCache)); }, []);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/parenting.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/safe.js":
/*!***************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/safe.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   safeProbe: () => (/* binding */ safeProbe)\n/* harmony export */ });\nvar safeProbe = function (cb) {\n    try {\n        return cb();\n    }\n    catch (e) {\n        return undefined;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvc2FmZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZXItZWRpdG9yLy4uLy4uL25vZGVfbW9kdWxlcy9mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L3V0aWxzL3NhZmUuanM/NzM5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIHNhZmVQcm9iZSA9IGZ1bmN0aW9uIChjYikge1xuICAgIHRyeSB7XG4gICAgICAgIHJldHVybiBjYigpO1xuICAgIH1cbiAgICBjYXRjaCAoZSkge1xuICAgICAgICByZXR1cm4gdW5kZWZpbmVkO1xuICAgIH1cbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/safe.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabOrder.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/tabOrder.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   orderByTabIndex: () => (/* binding */ orderByTabIndex),\n/* harmony export */   tabSort: () => (/* binding */ tabSort)\n/* harmony export */ });\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js\");\n\nvar tabSort = function (a, b) {\n    var aTab = Math.max(0, a.tabIndex);\n    var bTab = Math.max(0, b.tabIndex);\n    var tabDiff = aTab - bTab;\n    var indexDiff = a.index - b.index;\n    if (tabDiff) {\n        if (!aTab) {\n            return 1;\n        }\n        if (!bTab) {\n            return -1;\n        }\n    }\n    return tabDiff || indexDiff;\n};\nvar getTabIndex = function (node) {\n    if (node.tabIndex < 0) {\n        // all \"focusable\" elements are already preselected\n        // but some might have implicit negative tabIndex\n        // return 0 for <audio without tabIndex attribute - it is \"tabbable\"\n        if (!node.hasAttribute('tabindex')) {\n            return 0;\n        }\n    }\n    return node.tabIndex;\n};\nvar orderByTabIndex = function (nodes, filterNegative, keepGuards) {\n    return (0,_array__WEBPACK_IMPORTED_MODULE_0__.toArray)(nodes)\n        .map(function (node, index) {\n        var tabIndex = getTabIndex(node);\n        return {\n            node: node,\n            index: index,\n            tabIndex: keepGuards && tabIndex === -1 ? ((node.dataset || {}).focusGuard ? 0 : -1) : tabIndex,\n        };\n    })\n        .filter(function (data) { return !filterNegative || data.tabIndex >= 0; })\n        .sort(tabSort);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabOrder.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabUtils.js":
/*!*******************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/tabUtils.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFocusables: () => (/* binding */ getFocusables),\n/* harmony export */   getParentAutofocusables: () => (/* binding */ getParentAutofocusables)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../constants */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _array__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./array */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/array.js\");\n/* harmony import */ var _tabbables__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tabbables */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabbables.js\");\n\n\n\nvar queryTabbables = _tabbables__WEBPACK_IMPORTED_MODULE_0__.tabbables.join(',');\nvar queryGuardTabbables = \"\".concat(queryTabbables, \", [data-focus-guard]\");\nvar getFocusablesWithShadowDom = function (parent, withGuards) {\n    return (0,_array__WEBPACK_IMPORTED_MODULE_1__.toArray)((parent.shadowRoot || parent).children).reduce(function (acc, child) {\n        return acc.concat(child.matches(withGuards ? queryGuardTabbables : queryTabbables) ? [child] : [], getFocusablesWithShadowDom(child));\n    }, []);\n};\nvar getFocusablesWithIFrame = function (parent, withGuards) {\n    var _a;\n    // contentDocument of iframe will be null if current origin cannot access it\n    if (parent instanceof HTMLIFrameElement && ((_a = parent.contentDocument) === null || _a === void 0 ? void 0 : _a.body)) {\n        return getFocusables([parent.contentDocument.body], withGuards);\n    }\n    return [parent];\n};\nvar getFocusables = function (parents, withGuards) {\n    return parents.reduce(function (acc, parent) {\n        var _a;\n        var focusableWithShadowDom = getFocusablesWithShadowDom(parent, withGuards);\n        var focusableWithIframes = (_a = []).concat.apply(_a, focusableWithShadowDom.map(function (node) { return getFocusablesWithIFrame(node, withGuards); }));\n        return acc.concat(\n        // add all tabbables inside and within shadow DOMs in DOM order\n        focusableWithIframes, \n        // add if node is tabbable itself\n        parent.parentNode\n            ? (0,_array__WEBPACK_IMPORTED_MODULE_1__.toArray)(parent.parentNode.querySelectorAll(queryTabbables)).filter(function (node) { return node === parent; })\n            : []);\n    }, []);\n};\n/**\n * return a list of focusable nodes within an area marked as \"auto-focusable\"\n * @param parent\n */\nvar getParentAutofocusables = function (parent) {\n    var parentFocus = parent.querySelectorAll(\"[\".concat(_constants__WEBPACK_IMPORTED_MODULE_2__.FOCUS_AUTO, \"]\"));\n    return (0,_array__WEBPACK_IMPORTED_MODULE_1__.toArray)(parentFocus)\n        .map(function (node) { return getFocusables([node]); })\n        .reduce(function (acc, nodes) { return acc.concat(nodes); }, []);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvdGFiVXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBMEM7QUFDUjtBQUNNO0FBQ3hDLHFCQUFxQixpREFBUztBQUM5QjtBQUNBO0FBQ0EsV0FBVywrQ0FBTztBQUNsQjtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLDJHQUEyRyxtREFBbUQ7QUFDOUo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsK0NBQU8sOEVBQThFLHlCQUF5QjtBQUM1SDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCx5REFBeUQsa0RBQVU7QUFDbkUsV0FBVywrQ0FBTztBQUNsQiwrQkFBK0IsK0JBQStCO0FBQzlELHdDQUF3QywyQkFBMkI7QUFDbkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGVyLWVkaXRvci8uLi8uLi9ub2RlX21vZHVsZXMvZm9jdXMtbG9jay9kaXN0L2VzMjAxNS91dGlscy90YWJVdGlscy5qcz82OTc3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEZPQ1VTX0FVVE8gfSBmcm9tICcuLi9jb25zdGFudHMnO1xuaW1wb3J0IHsgdG9BcnJheSB9IGZyb20gJy4vYXJyYXknO1xuaW1wb3J0IHsgdGFiYmFibGVzIH0gZnJvbSAnLi90YWJiYWJsZXMnO1xudmFyIHF1ZXJ5VGFiYmFibGVzID0gdGFiYmFibGVzLmpvaW4oJywnKTtcbnZhciBxdWVyeUd1YXJkVGFiYmFibGVzID0gXCJcIi5jb25jYXQocXVlcnlUYWJiYWJsZXMsIFwiLCBbZGF0YS1mb2N1cy1ndWFyZF1cIik7XG52YXIgZ2V0Rm9jdXNhYmxlc1dpdGhTaGFkb3dEb20gPSBmdW5jdGlvbiAocGFyZW50LCB3aXRoR3VhcmRzKSB7XG4gICAgcmV0dXJuIHRvQXJyYXkoKHBhcmVudC5zaGFkb3dSb290IHx8IHBhcmVudCkuY2hpbGRyZW4pLnJlZHVjZShmdW5jdGlvbiAoYWNjLCBjaGlsZCkge1xuICAgICAgICByZXR1cm4gYWNjLmNvbmNhdChjaGlsZC5tYXRjaGVzKHdpdGhHdWFyZHMgPyBxdWVyeUd1YXJkVGFiYmFibGVzIDogcXVlcnlUYWJiYWJsZXMpID8gW2NoaWxkXSA6IFtdLCBnZXRGb2N1c2FibGVzV2l0aFNoYWRvd0RvbShjaGlsZCkpO1xuICAgIH0sIFtdKTtcbn07XG52YXIgZ2V0Rm9jdXNhYmxlc1dpdGhJRnJhbWUgPSBmdW5jdGlvbiAocGFyZW50LCB3aXRoR3VhcmRzKSB7XG4gICAgdmFyIF9hO1xuICAgIC8vIGNvbnRlbnREb2N1bWVudCBvZiBpZnJhbWUgd2lsbCBiZSBudWxsIGlmIGN1cnJlbnQgb3JpZ2luIGNhbm5vdCBhY2Nlc3MgaXRcbiAgICBpZiAocGFyZW50IGluc3RhbmNlb2YgSFRNTElGcmFtZUVsZW1lbnQgJiYgKChfYSA9IHBhcmVudC5jb250ZW50RG9jdW1lbnQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5ib2R5KSkge1xuICAgICAgICByZXR1cm4gZ2V0Rm9jdXNhYmxlcyhbcGFyZW50LmNvbnRlbnREb2N1bWVudC5ib2R5XSwgd2l0aEd1YXJkcyk7XG4gICAgfVxuICAgIHJldHVybiBbcGFyZW50XTtcbn07XG5leHBvcnQgdmFyIGdldEZvY3VzYWJsZXMgPSBmdW5jdGlvbiAocGFyZW50cywgd2l0aEd1YXJkcykge1xuICAgIHJldHVybiBwYXJlbnRzLnJlZHVjZShmdW5jdGlvbiAoYWNjLCBwYXJlbnQpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICB2YXIgZm9jdXNhYmxlV2l0aFNoYWRvd0RvbSA9IGdldEZvY3VzYWJsZXNXaXRoU2hhZG93RG9tKHBhcmVudCwgd2l0aEd1YXJkcyk7XG4gICAgICAgIHZhciBmb2N1c2FibGVXaXRoSWZyYW1lcyA9IChfYSA9IFtdKS5jb25jYXQuYXBwbHkoX2EsIGZvY3VzYWJsZVdpdGhTaGFkb3dEb20ubWFwKGZ1bmN0aW9uIChub2RlKSB7IHJldHVybiBnZXRGb2N1c2FibGVzV2l0aElGcmFtZShub2RlLCB3aXRoR3VhcmRzKTsgfSkpO1xuICAgICAgICByZXR1cm4gYWNjLmNvbmNhdChcbiAgICAgICAgLy8gYWRkIGFsbCB0YWJiYWJsZXMgaW5zaWRlIGFuZCB3aXRoaW4gc2hhZG93IERPTXMgaW4gRE9NIG9yZGVyXG4gICAgICAgIGZvY3VzYWJsZVdpdGhJZnJhbWVzLCBcbiAgICAgICAgLy8gYWRkIGlmIG5vZGUgaXMgdGFiYmFibGUgaXRzZWxmXG4gICAgICAgIHBhcmVudC5wYXJlbnROb2RlXG4gICAgICAgICAgICA/IHRvQXJyYXkocGFyZW50LnBhcmVudE5vZGUucXVlcnlTZWxlY3RvckFsbChxdWVyeVRhYmJhYmxlcykpLmZpbHRlcihmdW5jdGlvbiAobm9kZSkgeyByZXR1cm4gbm9kZSA9PT0gcGFyZW50OyB9KVxuICAgICAgICAgICAgOiBbXSk7XG4gICAgfSwgW10pO1xufTtcbi8qKlxuICogcmV0dXJuIGEgbGlzdCBvZiBmb2N1c2FibGUgbm9kZXMgd2l0aGluIGFuIGFyZWEgbWFya2VkIGFzIFwiYXV0by1mb2N1c2FibGVcIlxuICogQHBhcmFtIHBhcmVudFxuICovXG5leHBvcnQgdmFyIGdldFBhcmVudEF1dG9mb2N1c2FibGVzID0gZnVuY3Rpb24gKHBhcmVudCkge1xuICAgIHZhciBwYXJlbnRGb2N1cyA9IHBhcmVudC5xdWVyeVNlbGVjdG9yQWxsKFwiW1wiLmNvbmNhdChGT0NVU19BVVRPLCBcIl1cIikpO1xuICAgIHJldHVybiB0b0FycmF5KHBhcmVudEZvY3VzKVxuICAgICAgICAubWFwKGZ1bmN0aW9uIChub2RlKSB7IHJldHVybiBnZXRGb2N1c2FibGVzKFtub2RlXSk7IH0pXG4gICAgICAgIC5yZWR1Y2UoZnVuY3Rpb24gKGFjYywgbm9kZXMpIHsgcmV0dXJuIGFjYy5jb25jYXQobm9kZXMpOyB9LCBbXSk7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabUtils.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabbables.js":
/*!********************************************************************!*\
  !*** ../../node_modules/focus-lock/dist/es2015/utils/tabbables.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tabbables: () => (/* binding */ tabbables)\n/* harmony export */ });\n/**\n * list of the object to be considered as focusable\n */\nvar tabbables = [\n    'button:enabled',\n    'select:enabled',\n    'textarea:enabled',\n    'input:enabled',\n    // elements with explicit roles will also use explicit tabindex\n    // '[role=\"button\"]',\n    'a[href]',\n    'area[href]',\n    'summary',\n    'iframe',\n    'object',\n    'embed',\n    'audio[controls]',\n    'video[controls]',\n    '[tabindex]',\n    '[contenteditable]',\n    '[autofocus]',\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2ZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbHMvdGFiYmFibGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZXItZWRpdG9yLy4uLy4uL25vZGVfbW9kdWxlcy9mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L3V0aWxzL3RhYmJhYmxlcy5qcz8wODNkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogbGlzdCBvZiB0aGUgb2JqZWN0IHRvIGJlIGNvbnNpZGVyZWQgYXMgZm9jdXNhYmxlXG4gKi9cbmV4cG9ydCB2YXIgdGFiYmFibGVzID0gW1xuICAgICdidXR0b246ZW5hYmxlZCcsXG4gICAgJ3NlbGVjdDplbmFibGVkJyxcbiAgICAndGV4dGFyZWE6ZW5hYmxlZCcsXG4gICAgJ2lucHV0OmVuYWJsZWQnLFxuICAgIC8vIGVsZW1lbnRzIHdpdGggZXhwbGljaXQgcm9sZXMgd2lsbCBhbHNvIHVzZSBleHBsaWNpdCB0YWJpbmRleFxuICAgIC8vICdbcm9sZT1cImJ1dHRvblwiXScsXG4gICAgJ2FbaHJlZl0nLFxuICAgICdhcmVhW2hyZWZdJyxcbiAgICAnc3VtbWFyeScsXG4gICAgJ2lmcmFtZScsXG4gICAgJ29iamVjdCcsXG4gICAgJ2VtYmVkJyxcbiAgICAnYXVkaW9bY29udHJvbHNdJyxcbiAgICAndmlkZW9bY29udHJvbHNdJyxcbiAgICAnW3RhYmluZGV4XScsXG4gICAgJ1tjb250ZW50ZWRpdGFibGVdJyxcbiAgICAnW2F1dG9mb2N1c10nLFxuXTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/focus-lock/dist/es2015/utils/tabbables.js\n");

/***/ })

};
;