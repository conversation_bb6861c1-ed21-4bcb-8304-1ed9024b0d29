"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/demo/page",{

/***/ "(app-pages-browser)/./app/editor/components/Toolbar/EditorToolbar.tsx":
/*!*********************************************************!*\
  !*** ./app/editor/components/Toolbar/EditorToolbar.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditorToolbar: function() { return /* binding */ EditorToolbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/color-mode/color-mode-context.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/tooltip/tooltip.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/icon-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/button-group.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu-list.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu-item.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/ArrowBack.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Repeat.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/View.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Settings.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Download.mjs\");\n/* harmony import */ var _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stores/editorStore */ \"(app-pages-browser)/./lib/stores/editorStore.ts\");\n/* __next_internal_client_entry_do_not_use__ EditorToolbar auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction EditorToolbar(param) {\n    let { onPreview } = param;\n    _s();\n    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"gray.200\", \"gray.700\");\n    const bgColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"white\", \"gray.800\");\n    const { currentPage, currentBreakpoint, setBreakpoint, undo, redo, undoStack, redoStack } = (0,_lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_1__.useEditorStore)();\n    const breakpointIcons = {\n        desktop: \"\\uD83D\\uDDA5️\",\n        tablet: \"\\uD83D\\uDCF1\",\n        mobile: \"\\uD83D\\uDCF1\"\n    };\n    const handleSave = ()=>{\n        // TODO: Implement save functionality\n        console.log(\"Saving page...\", currentPage);\n    };\n    const handlePublish = ()=>{\n        // TODO: Implement publish functionality\n        console.log(\"Publishing page...\", currentPage);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        h: \"60px\",\n        bg: bgColor,\n        borderBottom: \"1px\",\n        borderColor: borderColor,\n        px: 4,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n            align: \"center\",\n            justify: \"space-between\",\n            h: \"100%\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                    align: \"center\",\n                    gap: 3,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                            label: \"Back to Dashboard\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                \"aria-label\": \"Back\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__.ArrowBackIcon, {}, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 21\n                                }, void 0),\n                                variant: \"ghost\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Divider, {\n                            orientation: \"vertical\",\n                            h: \"24px\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                            fontSize: \"sm\",\n                            fontWeight: \"medium\",\n                            color: \"gray.700\",\n                            children: (currentPage === null || currentPage === void 0 ? void 0 : currentPage.name) || \"Untitled Page\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                    align: \"center\",\n                    gap: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                            fontSize: \"xs\",\n                            color: \"gray.500\",\n                            mr: 2,\n                            children: \"Device:\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ButtonGroup, {\n                            size: \"sm\",\n                            isAttached: true,\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDDA5️\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: currentBreakpoint === \"desktop\" ? \"blue\" : \"gray\",\n                                    onClick: ()=>setBreakpoint(\"desktop\"),\n                                    fontSize: \"xs\",\n                                    children: \"Desktop\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: currentBreakpoint === \"tablet\" ? \"blue\" : \"gray\",\n                                    onClick: ()=>setBreakpoint(\"tablet\"),\n                                    fontSize: \"xs\",\n                                    children: \"Tablet\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: currentBreakpoint === \"mobile\" ? \"blue\" : \"gray\",\n                                    onClick: ()=>setBreakpoint(\"mobile\"),\n                                    fontSize: \"xs\",\n                                    children: \"Mobile\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                    align: \"center\",\n                    gap: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ButtonGroup, {\n                            size: \"sm\",\n                            variant: \"ghost\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                    label: \"Undo\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                        \"aria-label\": \"Undo\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__.ArrowBackIcon, {\n                                            transform: \"scaleX(-1)\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: undo,\n                                        isDisabled: undoStack.length === 0\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                    label: \"Redo\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                        \"aria-label\": \"Redo\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_12__.RepeatIcon, {}, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: redo,\n                                        isDisabled: redoStack.length === 0\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Divider, {\n                            orientation: \"vertical\",\n                            h: \"24px\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                            label: \"Preview\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                \"aria-label\": \"Preview\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__.ViewIcon, {}, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 21\n                                }, void 0),\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onPreview\n                            }, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Menu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.MenuButton, {\n                                    as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton,\n                                    \"aria-label\": \"Settings\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_16__.SettingsIcon, {}, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    variant: \"ghost\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.MenuList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.MenuItem, {\n                                            onClick: handleSave,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_19__.DownloadIcon, {\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Save Page\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.MenuItem, {\n                                            onClick: handlePublish,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__.ViewIcon, {\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Publish Page\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Divider, {\n                            orientation: \"vertical\",\n                            h: \"24px\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ButtonGroup, {\n                            size: \"sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleSave,\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    colorScheme: \"blue\",\n                                    onClick: handlePublish,\n                                    children: \"Publish\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorToolbar, \"MeYd6vzhnuAaY/PBWNQPZitJLII=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue,\n        _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_1__.useEditorStore\n    ];\n});\n_c = EditorToolbar;\nvar _c;\n$RefreshReg$(_c, \"EditorToolbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/editor/components/Toolbar/EditorToolbar.tsx\n"));

/***/ })

});