"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/demo/page",{

/***/ "(app-pages-browser)/./app/editor/components/Toolbar/EditorToolbar.tsx":
/*!*********************************************************!*\
  !*** ./app/editor/components/Toolbar/EditorToolbar.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditorToolbar: function() { return /* binding */ EditorToolbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/color-mode/color-mode-context.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/box/box.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/flex/flex.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/tooltip/tooltip.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/icon-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/divider/divider.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/typography/text.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/button-group.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/button/button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu-button.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu-list.mjs\");\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @chakra-ui/react */ \"(app-pages-browser)/../../node_modules/@chakra-ui/react/dist/esm/menu/menu-item.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/ArrowBack.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Repeat.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/View.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Settings.mjs\");\n/* harmony import */ var _chakra_ui_icons__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @chakra-ui/icons */ \"(app-pages-browser)/../../node_modules/@chakra-ui/icons/dist/esm/Download.mjs\");\n/* harmony import */ var _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stores/editorStore */ \"(app-pages-browser)/./lib/stores/editorStore.ts\");\n/* __next_internal_client_entry_do_not_use__ EditorToolbar auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction EditorToolbar(param) {\n    let { onPreview, onToggleHistory } = param;\n    _s();\n    const borderColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"gray.200\", \"gray.700\");\n    const bgColor = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue)(\"white\", \"gray.800\");\n    const { currentPage, currentBreakpoint, setBreakpoint, undo, redo, undoStack, redoStack } = (0,_lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_1__.useEditorStore)();\n    const breakpointIcons = {\n        desktop: \"\\uD83D\\uDDA5️\",\n        tablet: \"\\uD83D\\uDCF1\",\n        mobile: \"\\uD83D\\uDCF1\"\n    };\n    const handleSave = ()=>{\n        // TODO: Implement save functionality\n        console.log(\"Saving page...\", currentPage);\n    };\n    const handlePublish = ()=>{\n        // TODO: Implement publish functionality\n        console.log(\"Publishing page...\", currentPage);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        h: \"60px\",\n        bg: bgColor,\n        borderBottom: \"1px\",\n        borderColor: borderColor,\n        px: 4,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n            align: \"center\",\n            justify: \"space-between\",\n            h: \"100%\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                    align: \"center\",\n                    gap: 3,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                            label: \"Back to Dashboard\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                \"aria-label\": \"Back\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__.ArrowBackIcon, {}, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 21\n                                }, void 0),\n                                variant: \"ghost\",\n                                size: \"sm\"\n                            }, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Divider, {\n                            orientation: \"vertical\",\n                            h: \"24px\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                            fontSize: \"sm\",\n                            fontWeight: \"medium\",\n                            color: \"gray.700\",\n                            children: (currentPage === null || currentPage === void 0 ? void 0 : currentPage.name) || \"Untitled Page\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                    align: \"center\",\n                    gap: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_9__.Text, {\n                            fontSize: \"xs\",\n                            color: \"gray.500\",\n                            mr: 2,\n                            children: \"Device:\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ButtonGroup, {\n                            size: \"sm\",\n                            isAttached: true,\n                            variant: \"outline\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDDA5️\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: currentBreakpoint === \"desktop\" ? \"blue\" : \"gray\",\n                                    onClick: ()=>setBreakpoint(\"desktop\"),\n                                    fontSize: \"xs\",\n                                    children: \"Desktop\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: currentBreakpoint === \"tablet\" ? \"blue\" : \"gray\",\n                                    onClick: ()=>setBreakpoint(\"tablet\"),\n                                    fontSize: \"xs\",\n                                    children: \"Tablet\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 25\n                                    }, void 0),\n                                    colorScheme: currentBreakpoint === \"mobile\" ? \"blue\" : \"gray\",\n                                    onClick: ()=>setBreakpoint(\"mobile\"),\n                                    fontSize: \"xs\",\n                                    children: \"Mobile\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_4__.Flex, {\n                    align: \"center\",\n                    gap: 2,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ButtonGroup, {\n                            size: \"sm\",\n                            variant: \"ghost\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                    label: \"Undo\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                        \"aria-label\": \"Undo\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_7__.ArrowBackIcon, {\n                                            transform: \"scaleX(-1)\"\n                                        }, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: undo,\n                                        isDisabled: undoStack.length === 0\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                                    label: \"Redo\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                        \"aria-label\": \"Redo\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_12__.RepeatIcon, {}, void 0, false, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 23\n                                        }, void 0),\n                                        onClick: redo,\n                                        isDisabled: redoStack.length === 0\n                                    }, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Divider, {\n                            orientation: \"vertical\",\n                            h: \"24px\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_5__.Tooltip, {\n                            label: \"Preview\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                \"aria-label\": \"Preview\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__.ViewIcon, {}, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 21\n                                }, void 0),\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: onPreview\n                            }, void 0, false, {\n                                fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_14__.Menu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_15__.MenuButton, {\n                                    as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_6__.IconButton,\n                                    \"aria-label\": \"Settings\",\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_16__.SettingsIcon, {}, void 0, false, {\n                                        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 21\n                                    }, void 0),\n                                    variant: \"ghost\",\n                                    size: \"sm\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_17__.MenuList, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.MenuItem, {\n                                            onClick: handleSave,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_19__.DownloadIcon, {\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Save Page\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_18__.MenuItem, {\n                                            onClick: handlePublish,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_icons__WEBPACK_IMPORTED_MODULE_13__.ViewIcon, {\n                                                    mr: 2\n                                                }, void 0, false, {\n                                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Publish Page\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_8__.Divider, {\n                            orientation: \"vertical\",\n                            h: \"24px\"\n                        }, void 0, false, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_10__.ButtonGroup, {\n                            size: \"sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleSave,\n                                    children: \"Save\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_11__.Button, {\n                                    colorScheme: \"blue\",\n                                    onClick: handlePublish,\n                                    children: \"Publish\"\n                                }, void 0, false, {\n                                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"B:\\\\New Builder Project\\\\new-builder\\\\Micro-Services\\\\builder-editor\\\\app\\\\editor\\\\components\\\\Toolbar\\\\EditorToolbar.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorToolbar, \"MeYd6vzhnuAaY/PBWNQPZitJLII=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue,\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useColorModeValue,\n        _lib_stores_editorStore__WEBPACK_IMPORTED_MODULE_1__.useEditorStore\n    ];\n});\n_c = EditorToolbar;\nvar _c;\n$RefreshReg$(_c, \"EditorToolbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/editor/components/Toolbar/EditorToolbar.tsx\n"));

/***/ })

});