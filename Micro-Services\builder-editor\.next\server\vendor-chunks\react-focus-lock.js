"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-focus-lock";
exports.ids = ["vendor-chunks/react-focus-lock"];
exports.modules = {

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/../../node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prop-types */ \"(ssr)/../../node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! focus-lock/constants */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/util.js\");\n\n\n\n\n\nvar AutoFocusInside = function AutoFocusInside(_ref) {\n  var _ref$disabled = _ref.disabled,\n    disabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    children = _ref.children,\n    _ref$className = _ref.className,\n    className = _ref$className === void 0 ? undefined : _ref$className;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (0,_util__WEBPACK_IMPORTED_MODULE_2__.inlineProp)(focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_AUTO, !disabled), {\n    className: className\n  }), children);\n};\nAutoFocusInside.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().node).isRequired,\n  disabled: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().bool),\n  className: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().string)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AutoFocusInside);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvQXV0b0ZvY3VzSW5zaWRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTBEO0FBQ2hDO0FBQ1M7QUFDZTtBQUNkO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBbUIsUUFBUSw4RUFBUSxHQUFHLEVBQUUsaURBQVUsQ0FBQyw0REFBVTtBQUNuRjtBQUNBLEdBQUc7QUFDSDtBQUNBLDRCQUE0QixLQUFxQztBQUNqRSxZQUFZLHdEQUFjO0FBQzFCLFlBQVksd0RBQWM7QUFDMUIsYUFBYSwwREFBZ0I7QUFDN0IsRUFBRSxFQUFFLENBQUU7QUFDTixpRUFBZSxlQUFlIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRlci1lZGl0b3IvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvQXV0b0ZvY3VzSW5zaWRlLmpzP2U1OGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IFByb3BUeXBlcyBmcm9tICdwcm9wLXR5cGVzJztcbmltcG9ydCB7IEZPQ1VTX0FVVE8gfSBmcm9tICdmb2N1cy1sb2NrL2NvbnN0YW50cyc7XG5pbXBvcnQgeyBpbmxpbmVQcm9wIH0gZnJvbSAnLi91dGlsJztcbnZhciBBdXRvRm9jdXNJbnNpZGUgPSBmdW5jdGlvbiBBdXRvRm9jdXNJbnNpZGUoX3JlZikge1xuICB2YXIgX3JlZiRkaXNhYmxlZCA9IF9yZWYuZGlzYWJsZWQsXG4gICAgZGlzYWJsZWQgPSBfcmVmJGRpc2FibGVkID09PSB2b2lkIDAgPyBmYWxzZSA6IF9yZWYkZGlzYWJsZWQsXG4gICAgY2hpbGRyZW4gPSBfcmVmLmNoaWxkcmVuLFxuICAgIF9yZWYkY2xhc3NOYW1lID0gX3JlZi5jbGFzc05hbWUsXG4gICAgY2xhc3NOYW1lID0gX3JlZiRjbGFzc05hbWUgPT09IHZvaWQgMCA/IHVuZGVmaW5lZCA6IF9yZWYkY2xhc3NOYW1lO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwgX2V4dGVuZHMoe30sIGlubGluZVByb3AoRk9DVVNfQVVUTywgIWRpc2FibGVkKSwge1xuICAgIGNsYXNzTmFtZTogY2xhc3NOYW1lXG4gIH0pLCBjaGlsZHJlbik7XG59O1xuQXV0b0ZvY3VzSW5zaWRlLnByb3BUeXBlcyA9IHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIiA/IHtcbiAgY2hpbGRyZW46IFByb3BUeXBlcy5ub2RlLmlzUmVxdWlyZWQsXG4gIGRpc2FibGVkOiBQcm9wVHlwZXMuYm9vbCxcbiAgY2xhc3NOYW1lOiBQcm9wVHlwZXMuc3RyaW5nXG59IDoge307XG5leHBvcnQgZGVmYXVsdCBBdXRvRm9jdXNJbnNpZGU7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/Combination.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/Combination.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/../../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/../../node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Lock__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Lock */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/Lock.js\");\n/* harmony import */ var _Trap__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Trap */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/Trap.js\");\n\n\n\n\n\nvar FocusLockCombination = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(function FocusLockUICombination(props, ref) {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default().createElement(_Lock__WEBPACK_IMPORTED_MODULE_3__[\"default\"], (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n    sideCar: _Trap__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n    ref: ref\n  }, props));\n});\nvar _ref = _Lock__WEBPACK_IMPORTED_MODULE_3__[\"default\"].propTypes || {},\n  sideCar = _ref.sideCar,\n  propTypes = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(_ref, [\"sideCar\"]);\nFocusLockCombination.propTypes =  true ? propTypes : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FocusLockCombination);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvQ29tYmluYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFvRztBQUMxQztBQUNoQjtBQUNUO0FBQ0Y7QUFDL0Isd0NBQXdDLGlEQUFVO0FBQ2xELHNCQUFzQiwwREFBbUIsQ0FBQyw2Q0FBVyxFQUFFLDhFQUFRO0FBQy9ELGFBQWEsNkNBQVM7QUFDdEI7QUFDQSxHQUFHO0FBQ0gsQ0FBQztBQUNELFdBQVcsNkNBQVcsZ0JBQWdCO0FBQ3RDO0FBQ0EsY0FBYyxtR0FBNkI7QUFDM0MsaUNBQWlDLEtBQXFDLGVBQWUsQ0FBRTtBQUN2RixpRUFBZSxvQkFBb0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGVyLWVkaXRvci8uLi8uLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9Db21iaW5hdGlvbi5qcz8wNjIxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZSBmcm9tIFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZVwiO1xuaW1wb3J0IF9leHRlbmRzIGZyb20gXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2VzbS9leHRlbmRzXCI7XG5pbXBvcnQgUmVhY3QsIHsgZm9yd2FyZFJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBGb2N1c0xvY2tVSSBmcm9tICcuL0xvY2snO1xuaW1wb3J0IEZvY3VzVHJhcCBmcm9tICcuL1RyYXAnO1xudmFyIEZvY3VzTG9ja0NvbWJpbmF0aW9uID0gLyojX19QVVJFX18qL2ZvcndhcmRSZWYoZnVuY3Rpb24gRm9jdXNMb2NrVUlDb21iaW5hdGlvbihwcm9wcywgcmVmKSB7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChGb2N1c0xvY2tVSSwgX2V4dGVuZHMoe1xuICAgIHNpZGVDYXI6IEZvY3VzVHJhcCxcbiAgICByZWY6IHJlZlxuICB9LCBwcm9wcykpO1xufSk7XG52YXIgX3JlZiA9IEZvY3VzTG9ja1VJLnByb3BUeXBlcyB8fCB7fSxcbiAgc2lkZUNhciA9IF9yZWYuc2lkZUNhcixcbiAgcHJvcFR5cGVzID0gX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UoX3JlZiwgW1wic2lkZUNhclwiXSk7XG5Gb2N1c0xvY2tDb21iaW5hdGlvbi5wcm9wVHlwZXMgPSBwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIgPyBwcm9wVHlwZXMgOiB7fTtcbmV4cG9ydCBkZWZhdWx0IEZvY3VzTG9ja0NvbWJpbmF0aW9uOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/Combination.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/FocusGuard.js":
/*!*********************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/FocusGuard.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hiddenGuard: () => (/* binding */ hiddenGuard)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prop-types */ \"(ssr)/../../node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_1__);\n\n\nvar hiddenGuard = {\n  width: '1px',\n  height: '0px',\n  padding: 0,\n  overflow: 'hidden',\n  position: 'fixed',\n  top: '1px',\n  left: '1px'\n};\nvar InFocusGuard = function InFocusGuard(_ref) {\n  var _ref$children = _ref.children,\n    children = _ref$children === void 0 ? null : _ref$children;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    key: \"guard-first\",\n    \"data-focus-guard\": true,\n    \"data-focus-auto-guard\": true,\n    style: hiddenGuard\n  }), children, children && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    key: \"guard-last\",\n    \"data-focus-guard\": true,\n    \"data-focus-auto-guard\": true,\n    style: hiddenGuard\n  }));\n};\nInFocusGuard.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_1___default().node)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InFocusGuard);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvRm9jdXNHdWFyZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBd0M7QUFDTDtBQUM1QjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMERBQW1CLENBQUMsMkNBQVEscUJBQXFCLDBEQUFtQjtBQUMxRjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUcsc0NBQXNDLDBEQUFtQjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBLHlCQUF5QixLQUFxQztBQUM5RCxZQUFZLHdEQUFjO0FBQzFCLEVBQUUsRUFBRSxDQUFFO0FBQ04saUVBQWUsWUFBWSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZXItZWRpdG9yLy4uLy4uL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L0ZvY3VzR3VhcmQuanM/N2VmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgRnJhZ21lbnQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgUHJvcFR5cGVzIGZyb20gJ3Byb3AtdHlwZXMnO1xuZXhwb3J0IHZhciBoaWRkZW5HdWFyZCA9IHtcbiAgd2lkdGg6ICcxcHgnLFxuICBoZWlnaHQ6ICcwcHgnLFxuICBwYWRkaW5nOiAwLFxuICBvdmVyZmxvdzogJ2hpZGRlbicsXG4gIHBvc2l0aW9uOiAnZml4ZWQnLFxuICB0b3A6ICcxcHgnLFxuICBsZWZ0OiAnMXB4J1xufTtcbnZhciBJbkZvY3VzR3VhcmQgPSBmdW5jdGlvbiBJbkZvY3VzR3VhcmQoX3JlZikge1xuICB2YXIgX3JlZiRjaGlsZHJlbiA9IF9yZWYuY2hpbGRyZW4sXG4gICAgY2hpbGRyZW4gPSBfcmVmJGNoaWxkcmVuID09PSB2b2lkIDAgPyBudWxsIDogX3JlZiRjaGlsZHJlbjtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KEZyYWdtZW50LCBudWxsLCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAga2V5OiBcImd1YXJkLWZpcnN0XCIsXG4gICAgXCJkYXRhLWZvY3VzLWd1YXJkXCI6IHRydWUsXG4gICAgXCJkYXRhLWZvY3VzLWF1dG8tZ3VhcmRcIjogdHJ1ZSxcbiAgICBzdHlsZTogaGlkZGVuR3VhcmRcbiAgfSksIGNoaWxkcmVuLCBjaGlsZHJlbiAmJiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChcImRpdlwiLCB7XG4gICAga2V5OiBcImd1YXJkLWxhc3RcIixcbiAgICBcImRhdGEtZm9jdXMtZ3VhcmRcIjogdHJ1ZSxcbiAgICBcImRhdGEtZm9jdXMtYXV0by1ndWFyZFwiOiB0cnVlLFxuICAgIHN0eWxlOiBoaWRkZW5HdWFyZFxuICB9KSk7XG59O1xuSW5Gb2N1c0d1YXJkLnByb3BUeXBlcyA9IHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSBcInByb2R1Y3Rpb25cIiA/IHtcbiAgY2hpbGRyZW46IFByb3BUeXBlcy5ub2RlXG59IDoge307XG5leHBvcnQgZGVmYXVsdCBJbkZvY3VzR3VhcmQ7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/FocusGuard.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/../../node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prop-types */ \"(ssr)/../../node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! focus-lock/constants */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/util.js\");\n\n\n\n\n\nvar FreeFocusInside = function FreeFocusInside(_ref) {\n  var children = _ref.children,\n    className = _ref.className;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (0,_util__WEBPACK_IMPORTED_MODULE_2__.inlineProp)(focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_ALLOW, true), {\n    className: className\n  }), children);\n};\nFreeFocusInside.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().node).isRequired,\n  className: (prop_types__WEBPACK_IMPORTED_MODULE_4___default().string)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FreeFocusInside);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvRnJlZUZvY3VzSW5zaWRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTBEO0FBQ2hDO0FBQ1M7QUFDZ0I7QUFDZjtBQUNwQztBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMERBQW1CLFFBQVEsOEVBQVEsR0FBRyxFQUFFLGlEQUFVLENBQUMsNkRBQVc7QUFDcEY7QUFDQSxHQUFHO0FBQ0g7QUFDQSw0QkFBNEIsS0FBcUM7QUFDakUsWUFBWSx3REFBYztBQUMxQixhQUFhLDBEQUFnQjtBQUM3QixFQUFFLEVBQUUsQ0FBRTtBQUNOLGlFQUFlLGVBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGVyLWVkaXRvci8uLi8uLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9GcmVlRm9jdXNJbnNpZGUuanM/NWIyNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgX2V4dGVuZHMgZnJvbSBcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2V4dGVuZHNcIjtcbmltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgUHJvcFR5cGVzIGZyb20gJ3Byb3AtdHlwZXMnO1xuaW1wb3J0IHsgRk9DVVNfQUxMT1cgfSBmcm9tICdmb2N1cy1sb2NrL2NvbnN0YW50cyc7XG5pbXBvcnQgeyBpbmxpbmVQcm9wIH0gZnJvbSAnLi91dGlsJztcbnZhciBGcmVlRm9jdXNJbnNpZGUgPSBmdW5jdGlvbiBGcmVlRm9jdXNJbnNpZGUoX3JlZikge1xuICB2YXIgY2hpbGRyZW4gPSBfcmVmLmNoaWxkcmVuLFxuICAgIGNsYXNzTmFtZSA9IF9yZWYuY2xhc3NOYW1lO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoXCJkaXZcIiwgX2V4dGVuZHMoe30sIGlubGluZVByb3AoRk9DVVNfQUxMT1csIHRydWUpLCB7XG4gICAgY2xhc3NOYW1lOiBjbGFzc05hbWVcbiAgfSksIGNoaWxkcmVuKTtcbn07XG5GcmVlRm9jdXNJbnNpZGUucHJvcFR5cGVzID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiID8ge1xuICBjaGlsZHJlbjogUHJvcFR5cGVzLm5vZGUuaXNSZXF1aXJlZCxcbiAgY2xhc3NOYW1lOiBQcm9wVHlwZXMuc3RyaW5nXG59IDoge307XG5leHBvcnQgZGVmYXVsdCBGcmVlRm9jdXNJbnNpZGU7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/Lock.js":
/*!***************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/Lock.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/../../node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! prop-types */ \"(ssr)/../../node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! focus-lock/constants */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-callback-ref */ \"(ssr)/../../node_modules/use-callback-ref/dist/es2015/useMergeRef.js\");\n/* harmony import */ var _FocusGuard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FocusGuard */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/FocusGuard.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./medium */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/medium.js\");\n/* harmony import */ var _scope__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./scope */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/scope.js\");\n\n\n\n\n\n\n\n\nvar emptyArray = [];\nvar FocusLock = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function FocusLockUI(props, parentRef) {\n  var _extends2;\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(),\n    realObserved = _useState[0],\n    setObserved = _useState[1];\n  var observed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n  var isActive = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n  var originalFocusedElement = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}),\n    update = _useState2[1];\n  var children = props.children,\n    _props$disabled = props.disabled,\n    disabled = _props$disabled === void 0 ? false : _props$disabled,\n    _props$noFocusGuards = props.noFocusGuards,\n    noFocusGuards = _props$noFocusGuards === void 0 ? false : _props$noFocusGuards,\n    _props$persistentFocu = props.persistentFocus,\n    persistentFocus = _props$persistentFocu === void 0 ? false : _props$persistentFocu,\n    _props$crossFrame = props.crossFrame,\n    crossFrame = _props$crossFrame === void 0 ? true : _props$crossFrame,\n    _props$autoFocus = props.autoFocus,\n    autoFocus = _props$autoFocus === void 0 ? true : _props$autoFocus,\n    allowTextSelection = props.allowTextSelection,\n    group = props.group,\n    className = props.className,\n    whiteList = props.whiteList,\n    hasPositiveIndices = props.hasPositiveIndices,\n    _props$shards = props.shards,\n    shards = _props$shards === void 0 ? emptyArray : _props$shards,\n    _props$as = props.as,\n    Container = _props$as === void 0 ? 'div' : _props$as,\n    _props$lockProps = props.lockProps,\n    containerProps = _props$lockProps === void 0 ? {} : _props$lockProps,\n    SideCar = props.sideCar,\n    _props$returnFocus = props.returnFocus,\n    shouldReturnFocus = _props$returnFocus === void 0 ? false : _props$returnFocus,\n    focusOptions = props.focusOptions,\n    onActivationCallback = props.onActivation,\n    onDeactivationCallback = props.onDeactivation;\n  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}),\n    id = _useState3[0];\n  var onActivation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (_ref) {\n    var captureFocusRestore = _ref.captureFocusRestore;\n    if (!originalFocusedElement.current) {\n      var _document;\n      var activeElement = (_document = document) == null ? void 0 : _document.activeElement;\n      originalFocusedElement.current = activeElement;\n      if (activeElement !== document.body) {\n        originalFocusedElement.current = captureFocusRestore(activeElement);\n      }\n    }\n    if (observed.current && onActivationCallback) {\n      onActivationCallback(observed.current);\n    }\n    isActive.current = true;\n    update();\n  }, [onActivationCallback]);\n  var onDeactivation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {\n    isActive.current = false;\n    if (onDeactivationCallback) {\n      onDeactivationCallback(observed.current);\n    }\n    update();\n  }, [onDeactivationCallback]);\n  var returnFocus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (allowDefer) {\n    var focusRestore = originalFocusedElement.current;\n    if (focusRestore) {\n      var returnFocusTo = (typeof focusRestore === 'function' ? focusRestore() : focusRestore) || document.body;\n      var howToReturnFocus = typeof shouldReturnFocus === 'function' ? shouldReturnFocus(returnFocusTo) : shouldReturnFocus;\n      if (howToReturnFocus) {\n        var returnFocusOptions = typeof howToReturnFocus === 'object' ? howToReturnFocus : undefined;\n        originalFocusedElement.current = null;\n        if (allowDefer) {\n          Promise.resolve().then(function () {\n            return returnFocusTo.focus(returnFocusOptions);\n          });\n        } else {\n          returnFocusTo.focus(returnFocusOptions);\n        }\n      }\n    }\n  }, [shouldReturnFocus]);\n  var onFocus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (event) {\n    if (isActive.current) {\n      _medium__WEBPACK_IMPORTED_MODULE_2__.mediumFocus.useMedium(event);\n    }\n  }, []);\n  var onBlur = _medium__WEBPACK_IMPORTED_MODULE_2__.mediumBlur.useMedium;\n  var setObserveNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (newObserved) {\n    if (observed.current !== newObserved) {\n      observed.current = newObserved;\n      setObserved(newObserved);\n    }\n  }, []);\n  if (true) {\n    if (typeof allowTextSelection !== 'undefined') {\n      console.warn('React-Focus-Lock: allowTextSelection is deprecated and enabled by default');\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n      if (!observed.current && typeof Container !== 'string') {\n        console.error('FocusLock: could not obtain ref to internal node');\n      }\n    }, []);\n  }\n  var lockProps = (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])((_extends2 = {}, _extends2[focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_DISABLED] = disabled && 'disabled', _extends2[focus_lock_constants__WEBPACK_IMPORTED_MODULE_3__.FOCUS_GROUP] = group, _extends2), containerProps);\n  var hasLeadingGuards = noFocusGuards !== true;\n  var hasTailingGuards = hasLeadingGuards && noFocusGuards !== 'tail';\n  var mergedRef = (0,use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useMergeRefs)([parentRef, setObserveNode]);\n  var focusScopeValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return {\n      observed: observed,\n      shards: shards,\n      enabled: !disabled,\n      active: isActive.current\n    };\n  }, [disabled, isActive.current, shards, realObserved]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, hasLeadingGuards && [\n  /*#__PURE__*/\n  react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    key: \"guard-first\",\n    \"data-focus-guard\": true,\n    tabIndex: disabled ? -1 : 0,\n    style: _FocusGuard__WEBPACK_IMPORTED_MODULE_5__.hiddenGuard\n  }), hasPositiveIndices ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    key: \"guard-nearest\",\n    \"data-focus-guard\": true,\n    tabIndex: disabled ? -1 : 1,\n    style: _FocusGuard__WEBPACK_IMPORTED_MODULE_5__.hiddenGuard\n  }) : null], !disabled && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(SideCar, {\n    id: id,\n    sideCar: _medium__WEBPACK_IMPORTED_MODULE_2__.mediumSidecar,\n    observed: realObserved,\n    disabled: disabled,\n    persistentFocus: persistentFocus,\n    crossFrame: crossFrame,\n    autoFocus: autoFocus,\n    whiteList: whiteList,\n    shards: shards,\n    onActivation: onActivation,\n    onDeactivation: onDeactivation,\n    returnFocus: returnFocus,\n    focusOptions: focusOptions,\n    noFocusGuards: noFocusGuards\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(Container, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    ref: mergedRef\n  }, lockProps, {\n    className: className,\n    onBlur: onBlur,\n    onFocus: onFocus\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(_scope__WEBPACK_IMPORTED_MODULE_6__.focusScope.Provider, {\n    value: focusScopeValue\n  }, children)), hasTailingGuards && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", {\n    \"data-focus-guard\": true,\n    tabIndex: disabled ? -1 : 0,\n    style: _FocusGuard__WEBPACK_IMPORTED_MODULE_5__.hiddenGuard\n  }));\n});\nFocusLock.propTypes =  true ? {\n  children: prop_types__WEBPACK_IMPORTED_MODULE_7__.node,\n  disabled: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  returnFocus: (0,prop_types__WEBPACK_IMPORTED_MODULE_7__.oneOfType)([prop_types__WEBPACK_IMPORTED_MODULE_7__.bool, prop_types__WEBPACK_IMPORTED_MODULE_7__.object, prop_types__WEBPACK_IMPORTED_MODULE_7__.func]),\n  focusOptions: prop_types__WEBPACK_IMPORTED_MODULE_7__.object,\n  noFocusGuards: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  hasPositiveIndices: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  allowTextSelection: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  autoFocus: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  persistentFocus: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  crossFrame: prop_types__WEBPACK_IMPORTED_MODULE_7__.bool,\n  group: prop_types__WEBPACK_IMPORTED_MODULE_7__.string,\n  className: prop_types__WEBPACK_IMPORTED_MODULE_7__.string,\n  whiteList: prop_types__WEBPACK_IMPORTED_MODULE_7__.func,\n  shards: (0,prop_types__WEBPACK_IMPORTED_MODULE_7__.arrayOf)(prop_types__WEBPACK_IMPORTED_MODULE_7__.any),\n  as: (0,prop_types__WEBPACK_IMPORTED_MODULE_7__.oneOfType)([prop_types__WEBPACK_IMPORTED_MODULE_7__.string, prop_types__WEBPACK_IMPORTED_MODULE_7__.func, prop_types__WEBPACK_IMPORTED_MODULE_7__.object]),\n  lockProps: prop_types__WEBPACK_IMPORTED_MODULE_7__.object,\n  onActivation: prop_types__WEBPACK_IMPORTED_MODULE_7__.func,\n  onDeactivation: prop_types__WEBPACK_IMPORTED_MODULE_7__.func,\n  sideCar: prop_types__WEBPACK_IMPORTED_MODULE_7__.any.isRequired\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FocusLock);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/Lock.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useFocusInside: () => (/* binding */ useFocusInside)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/../../node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! prop-types */ \"(ssr)/../../node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var focus_lock_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! focus-lock/constants */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/constants.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/util.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./medium */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/medium.js\");\n\n\n\n\n\n\nvar useFocusInside = function useFocusInside(observedRef) {\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {\n    var enabled = true;\n    _medium__WEBPACK_IMPORTED_MODULE_2__.mediumEffect.useMedium(function (car) {\n      var observed = observedRef && observedRef.current;\n      if (enabled && observed) {\n        if (!car.focusInside(observed)) {\n          car.moveFocusInside(observed, null);\n        }\n      }\n    });\n    return function () {\n      enabled = false;\n    };\n  }, [observedRef]);\n};\nfunction MoveFocusInside(_ref) {\n  var _ref$disabled = _ref.disabled,\n    isDisabled = _ref$disabled === void 0 ? false : _ref$disabled,\n    className = _ref.className,\n    children = _ref.children;\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n  useFocusInside(isDisabled ? undefined : ref);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default().createElement(\"div\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, (0,_util__WEBPACK_IMPORTED_MODULE_3__.inlineProp)(focus_lock_constants__WEBPACK_IMPORTED_MODULE_4__.FOCUS_AUTO, !isDisabled), {\n    ref: ref,\n    className: className\n  }), children);\n}\nMoveFocusInside.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().node).isRequired,\n  disabled: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().bool),\n  className: (prop_types__WEBPACK_IMPORTED_MODULE_5___default().string)\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MoveFocusInside);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/Trap.js":
/*!***************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/Trap.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! prop-types */ \"(ssr)/../../node_modules/prop-types/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react_clientside_effect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react-clientside-effect */ \"(ssr)/../../node_modules/react-clientside-effect/lib/index.es.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! focus-lock */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/focusIsHidden.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! focus-lock */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/utils/DOMutils.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! focus-lock */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/focusInside.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! focus-lock */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/moveFocusInside.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! focus-lock */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/return-focus.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! focus-lock */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/focusables.js\");\n/* harmony import */ var focus_lock__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! focus-lock */ \"(ssr)/../../node_modules/focus-lock/dist/es2015/sibling.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./util */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/util.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./medium */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/medium.js\");\n\n\n\n\n\n\nvar focusOnBody = function focusOnBody() {\n  return document && document.activeElement === document.body;\n};\nvar isFreeFocus = function isFreeFocus() {\n  return focusOnBody() || (0,focus_lock__WEBPACK_IMPORTED_MODULE_1__.focusIsHidden)();\n};\nvar lastActiveTrap = null;\nvar lastActiveFocus = null;\nvar tryRestoreFocus = function tryRestoreFocus() {\n  return null;\n};\nvar lastPortaledElement = null;\nvar focusWasOutsideWindow = false;\nvar windowFocused = false;\nvar defaultWhitelist = function defaultWhitelist() {\n  return true;\n};\nvar focusWhitelisted = function focusWhitelisted(activeElement) {\n  return (lastActiveTrap.whiteList || defaultWhitelist)(activeElement);\n};\nvar recordPortal = function recordPortal(observerNode, portaledElement) {\n  lastPortaledElement = {\n    observerNode: observerNode,\n    portaledElement: portaledElement\n  };\n};\nvar focusIsPortaledPair = function focusIsPortaledPair(element) {\n  return lastPortaledElement && lastPortaledElement.portaledElement === element;\n};\nfunction autoGuard(startIndex, end, step, allNodes) {\n  var lastGuard = null;\n  var i = startIndex;\n  do {\n    var item = allNodes[i];\n    if (item.guard) {\n      if (item.node.dataset.focusAutoGuard) {\n        lastGuard = item;\n      }\n    } else if (item.lockItem) {\n      if (i !== startIndex) {\n        return;\n      }\n      lastGuard = null;\n    } else {\n      break;\n    }\n  } while ((i += step) !== end);\n  if (lastGuard) {\n    lastGuard.node.tabIndex = 0;\n  }\n}\nvar focusWasOutside = function focusWasOutside(crossFrameOption) {\n  if (crossFrameOption) {\n    return Boolean(focusWasOutsideWindow);\n  }\n  return focusWasOutsideWindow === 'meanwhile';\n};\nvar checkInHost = function checkInHost(check, el, boundary) {\n  return el && (el.host === check && (!el.activeElement || boundary.contains(el.activeElement)) || el.parentNode && checkInHost(check, el.parentNode, boundary));\n};\nvar withinHost = function withinHost(activeElement, workingArea) {\n  return workingArea.some(function (area) {\n    return checkInHost(activeElement, area, area);\n  });\n};\nvar getNodeFocusables = function getNodeFocusables(nodes) {\n  return (0,focus_lock__WEBPACK_IMPORTED_MODULE_2__.getFocusableNodes)(nodes, new Map());\n};\nvar isNotFocusable = function isNotFocusable(node) {\n  return !getNodeFocusables([node.parentNode]).some(function (el) {\n    return el.node === node;\n  });\n};\nvar activateTrap = function activateTrap() {\n  var result = false;\n  if (lastActiveTrap) {\n    var _lastActiveTrap = lastActiveTrap,\n      observed = _lastActiveTrap.observed,\n      persistentFocus = _lastActiveTrap.persistentFocus,\n      autoFocus = _lastActiveTrap.autoFocus,\n      shards = _lastActiveTrap.shards,\n      crossFrame = _lastActiveTrap.crossFrame,\n      focusOptions = _lastActiveTrap.focusOptions,\n      noFocusGuards = _lastActiveTrap.noFocusGuards;\n    var workingNode = observed || lastPortaledElement && lastPortaledElement.portaledElement;\n    if (focusOnBody() && lastActiveFocus && lastActiveFocus !== document.body) {\n      if (!document.body.contains(lastActiveFocus) || isNotFocusable(lastActiveFocus)) {\n        var newTarget = tryRestoreFocus();\n        if (newTarget) {\n          newTarget.focus();\n        }\n      }\n    }\n    var activeElement = document && document.activeElement;\n    if (workingNode) {\n      var workingArea = [workingNode].concat(shards.map(_util__WEBPACK_IMPORTED_MODULE_3__.extractRef).filter(Boolean));\n      var shouldForceRestoreFocus = function shouldForceRestoreFocus() {\n        if (!focusWasOutside(crossFrame) || !noFocusGuards || !lastActiveFocus || windowFocused) {\n          return false;\n        }\n        var nodes = getNodeFocusables(workingArea);\n        var lastIndex = nodes.findIndex(function (_ref) {\n          var node = _ref.node;\n          return node === lastActiveFocus;\n        });\n        return lastIndex === 0 || lastIndex === nodes.length - 1;\n      };\n      if (!activeElement || focusWhitelisted(activeElement)) {\n        if (persistentFocus || shouldForceRestoreFocus() || !isFreeFocus() || !lastActiveFocus && autoFocus) {\n          if (workingNode && !((0,focus_lock__WEBPACK_IMPORTED_MODULE_4__.focusInside)(workingArea) || activeElement && withinHost(activeElement, workingArea) || focusIsPortaledPair(activeElement, workingNode))) {\n            if (document && !lastActiveFocus && activeElement && !autoFocus) {\n              if (activeElement.blur) {\n                activeElement.blur();\n              }\n              document.body.focus();\n            } else {\n              result = (0,focus_lock__WEBPACK_IMPORTED_MODULE_5__.moveFocusInside)(workingArea, lastActiveFocus, {\n                focusOptions: focusOptions\n              });\n              lastPortaledElement = {};\n            }\n          }\n          lastActiveFocus = document && document.activeElement;\n          if (lastActiveFocus !== document.body) {\n            tryRestoreFocus = (0,focus_lock__WEBPACK_IMPORTED_MODULE_6__.captureFocusRestore)(lastActiveFocus);\n          }\n          focusWasOutsideWindow = false;\n        }\n      }\n      if (document && activeElement !== document.activeElement && document.querySelector('[data-focus-auto-guard]')) {\n        var newActiveElement = document && document.activeElement;\n        var allNodes = (0,focus_lock__WEBPACK_IMPORTED_MODULE_7__.expandFocusableNodes)(workingArea);\n        var focusedIndex = allNodes.map(function (_ref2) {\n          var node = _ref2.node;\n          return node;\n        }).indexOf(newActiveElement);\n        if (focusedIndex > -1) {\n          allNodes.filter(function (_ref3) {\n            var guard = _ref3.guard,\n              node = _ref3.node;\n            return guard && node.dataset.focusAutoGuard;\n          }).forEach(function (_ref4) {\n            var node = _ref4.node;\n            return node.removeAttribute('tabIndex');\n          });\n          autoGuard(focusedIndex, allNodes.length, +1, allNodes);\n          autoGuard(focusedIndex, -1, -1, allNodes);\n        }\n      }\n    }\n  }\n  return result;\n};\nvar onTrap = function onTrap(event) {\n  if (activateTrap() && event) {\n    event.stopPropagation();\n    event.preventDefault();\n  }\n};\nvar onBlur = function onBlur() {\n  return (0,_util__WEBPACK_IMPORTED_MODULE_3__.deferAction)(activateTrap);\n};\nvar onFocus = function onFocus(event) {\n  var source = event.target;\n  var currentNode = event.currentTarget;\n  if (!currentNode.contains(source)) {\n    recordPortal(currentNode, source);\n  }\n};\nvar FocusWatcher = function FocusWatcher() {\n  return null;\n};\nvar FocusTrap = function FocusTrap(_ref5) {\n  var children = _ref5.children;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    onBlur: onBlur,\n    onFocus: onFocus\n  }, children);\n};\nFocusTrap.propTypes =  true ? {\n  children: (prop_types__WEBPACK_IMPORTED_MODULE_8___default().node).isRequired\n} : 0;\nvar onWindowFocus = function onWindowFocus() {\n  windowFocused = true;\n};\nvar onWindowBlur = function onWindowBlur() {\n  windowFocused = false;\n  focusWasOutsideWindow = 'just';\n  (0,_util__WEBPACK_IMPORTED_MODULE_3__.deferAction)(function () {\n    focusWasOutsideWindow = 'meanwhile';\n  });\n};\nvar attachHandler = function attachHandler() {\n  document.addEventListener('focusin', onTrap);\n  document.addEventListener('focusout', onBlur);\n  window.addEventListener('focus', onWindowFocus);\n  window.addEventListener('blur', onWindowBlur);\n};\nvar detachHandler = function detachHandler() {\n  document.removeEventListener('focusin', onTrap);\n  document.removeEventListener('focusout', onBlur);\n  window.removeEventListener('focus', onWindowFocus);\n  window.removeEventListener('blur', onWindowBlur);\n};\nfunction reducePropsToState(propsList) {\n  return propsList.filter(function (_ref6) {\n    var disabled = _ref6.disabled;\n    return !disabled;\n  });\n}\nvar focusLockAPI = {\n  moveFocusInside: focus_lock__WEBPACK_IMPORTED_MODULE_5__.moveFocusInside,\n  focusInside: focus_lock__WEBPACK_IMPORTED_MODULE_4__.focusInside,\n  focusNextElement: focus_lock__WEBPACK_IMPORTED_MODULE_9__.focusNextElement,\n  focusPrevElement: focus_lock__WEBPACK_IMPORTED_MODULE_9__.focusPrevElement,\n  focusFirstElement: focus_lock__WEBPACK_IMPORTED_MODULE_9__.focusFirstElement,\n  focusLastElement: focus_lock__WEBPACK_IMPORTED_MODULE_9__.focusLastElement,\n  captureFocusRestore: focus_lock__WEBPACK_IMPORTED_MODULE_6__.captureFocusRestore\n};\nfunction handleStateChangeOnClient(traps) {\n  var trap = traps.slice(-1)[0];\n  if (trap && !lastActiveTrap) {\n    attachHandler();\n  }\n  var lastTrap = lastActiveTrap;\n  var sameTrap = lastTrap && trap && trap.id === lastTrap.id;\n  lastActiveTrap = trap;\n  if (lastTrap && !sameTrap) {\n    lastTrap.onDeactivation();\n    if (!traps.filter(function (_ref7) {\n      var id = _ref7.id;\n      return id === lastTrap.id;\n    }).length) {\n      lastTrap.returnFocus(!trap);\n    }\n  }\n  if (trap) {\n    lastActiveFocus = null;\n    if (!sameTrap || lastTrap.observed !== trap.observed) {\n      trap.onActivation(focusLockAPI);\n    }\n    activateTrap(true);\n    (0,_util__WEBPACK_IMPORTED_MODULE_3__.deferAction)(activateTrap);\n  } else {\n    detachHandler();\n    lastActiveFocus = null;\n  }\n}\n_medium__WEBPACK_IMPORTED_MODULE_10__.mediumFocus.assignSyncMedium(onFocus);\n_medium__WEBPACK_IMPORTED_MODULE_10__.mediumBlur.assignMedium(onBlur);\n_medium__WEBPACK_IMPORTED_MODULE_10__.mediumEffect.assignMedium(function (cb) {\n  return cb(focusLockAPI);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_clientside_effect__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(reducePropsToState, handleStateChangeOnClient)(FocusWatcher));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/Trap.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/UI.js":
/*!*************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/UI.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoFocusInside: () => (/* reexport safe */ _AutoFocusInside__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   FocusLockUI: () => (/* reexport safe */ _Lock__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   FreeFocusInside: () => (/* reexport safe */ _FreeFocusInside__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   InFocusGuard: () => (/* reexport safe */ _FocusGuard__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   MoveFocusInside: () => (/* reexport safe */ _MoveFocusInside__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useFocusController: () => (/* reexport safe */ _use_focus_scope__WEBPACK_IMPORTED_MODULE_5__.useFocusController),\n/* harmony export */   useFocusInside: () => (/* reexport safe */ _MoveFocusInside__WEBPACK_IMPORTED_MODULE_1__.useFocusInside),\n/* harmony export */   useFocusScope: () => (/* reexport safe */ _use_focus_scope__WEBPACK_IMPORTED_MODULE_5__.useFocusScope),\n/* harmony export */   useFocusState: () => (/* reexport safe */ _use_focus_state__WEBPACK_IMPORTED_MODULE_6__.useFocusState)\n/* harmony export */ });\n/* harmony import */ var _Lock__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Lock */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/Lock.js\");\n/* harmony import */ var _AutoFocusInside__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./AutoFocusInside */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/AutoFocusInside.js\");\n/* harmony import */ var _MoveFocusInside__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./MoveFocusInside */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/MoveFocusInside.js\");\n/* harmony import */ var _FreeFocusInside__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FreeFocusInside */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/FreeFocusInside.js\");\n/* harmony import */ var _FocusGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FocusGuard */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/FocusGuard.js\");\n/* harmony import */ var _use_focus_scope__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./use-focus-scope */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/use-focus-scope.js\");\n/* harmony import */ var _use_focus_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./use-focus-state */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/use-focus-state.js\");\n\n\n\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Lock__WEBPACK_IMPORTED_MODULE_4__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvVUkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUM7QUFDZTtBQUNvQjtBQUNwQjtBQUNSO0FBQzhCO0FBQ3BCO0FBQ3dHO0FBQzFKLGlFQUFlLDZDQUFXIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRlci1lZGl0b3IvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvVUkuanM/ZjRiMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgRm9jdXNMb2NrVUkgZnJvbSAnLi9Mb2NrJztcbmltcG9ydCBBdXRvRm9jdXNJbnNpZGUgZnJvbSAnLi9BdXRvRm9jdXNJbnNpZGUnO1xuaW1wb3J0IE1vdmVGb2N1c0luc2lkZSwgeyB1c2VGb2N1c0luc2lkZSB9IGZyb20gJy4vTW92ZUZvY3VzSW5zaWRlJztcbmltcG9ydCBGcmVlRm9jdXNJbnNpZGUgZnJvbSAnLi9GcmVlRm9jdXNJbnNpZGUnO1xuaW1wb3J0IEluRm9jdXNHdWFyZCBmcm9tICcuL0ZvY3VzR3VhcmQnO1xuaW1wb3J0IHsgdXNlRm9jdXNDb250cm9sbGVyLCB1c2VGb2N1c1Njb3BlIH0gZnJvbSAnLi91c2UtZm9jdXMtc2NvcGUnO1xuaW1wb3J0IHsgdXNlRm9jdXNTdGF0ZSB9IGZyb20gJy4vdXNlLWZvY3VzLXN0YXRlJztcbmV4cG9ydCB7IEF1dG9Gb2N1c0luc2lkZSwgTW92ZUZvY3VzSW5zaWRlLCBGcmVlRm9jdXNJbnNpZGUsIEluRm9jdXNHdWFyZCwgRm9jdXNMb2NrVUksIHVzZUZvY3VzSW5zaWRlLCB1c2VGb2N1c0NvbnRyb2xsZXIsIHVzZUZvY3VzU2NvcGUsIHVzZUZvY3VzU3RhdGUgfTtcbmV4cG9ydCBkZWZhdWx0IEZvY3VzTG9ja1VJOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/UI.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/index.js":
/*!****************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AutoFocusInside: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.AutoFocusInside),\n/* harmony export */   FocusLockUI: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.FocusLockUI),\n/* harmony export */   FreeFocusInside: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.FreeFocusInside),\n/* harmony export */   InFocusGuard: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.InFocusGuard),\n/* harmony export */   MoveFocusInside: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.MoveFocusInside),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useFocusController: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.useFocusController),\n/* harmony export */   useFocusInside: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.useFocusInside),\n/* harmony export */   useFocusScope: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.useFocusScope),\n/* harmony export */   useFocusState: () => (/* reexport safe */ _UI__WEBPACK_IMPORTED_MODULE_0__.useFocusState)\n/* harmony export */ });\n/* harmony import */ var _Combination__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Combination */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/Combination.js\");\n/* harmony import */ var _UI__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./UI */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/UI.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_Combination__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXNDO0FBQ2pCO0FBQ3JCLGlFQUFlLG9EQUFTIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRlci1lZGl0b3IvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvaW5kZXguanM/NGI3MCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgRm9jdXNMb2NrIGZyb20gJy4vQ29tYmluYXRpb24nO1xuZXhwb3J0ICogZnJvbSAnLi9VSSc7XG5leHBvcnQgZGVmYXVsdCBGb2N1c0xvY2s7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/medium.js":
/*!*****************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/medium.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mediumBlur: () => (/* binding */ mediumBlur),\n/* harmony export */   mediumEffect: () => (/* binding */ mediumEffect),\n/* harmony export */   mediumFocus: () => (/* binding */ mediumFocus),\n/* harmony export */   mediumSidecar: () => (/* binding */ mediumSidecar)\n/* harmony export */ });\n/* harmony import */ var use_sidecar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sidecar */ \"(ssr)/../../node_modules/use-sidecar/dist/es2015/medium.js\");\n\nvar mediumFocus = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createMedium)({}, function (_ref) {\n  var target = _ref.target,\n    currentTarget = _ref.currentTarget;\n  return {\n    target: target,\n    currentTarget: currentTarget\n  };\n});\nvar mediumBlur = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createMedium)();\nvar mediumEffect = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createMedium)();\nvar mediumSidecar = (0,use_sidecar__WEBPACK_IMPORTED_MODULE_0__.createSidecarMedium)({\n  async: true,\n  ssr: typeof document !== 'undefined'\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvbWVkaXVtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQWdFO0FBQ3pELGtCQUFrQix5REFBWSxHQUFHO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDTSxpQkFBaUIseURBQVk7QUFDN0IsbUJBQW1CLHlEQUFZO0FBQy9CLG9CQUFvQixnRUFBbUI7QUFDOUM7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWlsZGVyLWVkaXRvci8uLi8uLi9ub2RlX21vZHVsZXMvcmVhY3QtZm9jdXMtbG9jay9kaXN0L2VzMjAxNS9tZWRpdW0uanM/NzEyZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVNZWRpdW0sIGNyZWF0ZVNpZGVjYXJNZWRpdW0gfSBmcm9tICd1c2Utc2lkZWNhcic7XG5leHBvcnQgdmFyIG1lZGl1bUZvY3VzID0gY3JlYXRlTWVkaXVtKHt9LCBmdW5jdGlvbiAoX3JlZikge1xuICB2YXIgdGFyZ2V0ID0gX3JlZi50YXJnZXQsXG4gICAgY3VycmVudFRhcmdldCA9IF9yZWYuY3VycmVudFRhcmdldDtcbiAgcmV0dXJuIHtcbiAgICB0YXJnZXQ6IHRhcmdldCxcbiAgICBjdXJyZW50VGFyZ2V0OiBjdXJyZW50VGFyZ2V0XG4gIH07XG59KTtcbmV4cG9ydCB2YXIgbWVkaXVtQmx1ciA9IGNyZWF0ZU1lZGl1bSgpO1xuZXhwb3J0IHZhciBtZWRpdW1FZmZlY3QgPSBjcmVhdGVNZWRpdW0oKTtcbmV4cG9ydCB2YXIgbWVkaXVtU2lkZWNhciA9IGNyZWF0ZVNpZGVjYXJNZWRpdW0oe1xuICBhc3luYzogdHJ1ZSxcbiAgc3NyOiB0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnXG59KTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/medium.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/nano-events.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/nano-events.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNanoEvents: () => (/* binding */ createNanoEvents)\n/* harmony export */ });\nvar createNanoEvents = function createNanoEvents() {\n  return {\n    emit: function emit(event) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      for (var i = 0, callbacks = this.events[event] || [], length = callbacks.length; i < length; i++) {\n        callbacks[i].apply(callbacks, args);\n      }\n    },\n    events: {},\n    on: function on(event, cb) {\n      var _this$events,\n        _this = this;\n      ((_this$events = this.events)[event] || (_this$events[event] = [])).push(cb);\n      return function () {\n        var _this$events$event;\n        _this.events[event] = (_this$events$event = _this.events[event]) == null ? void 0 : _this$events$event.filter(function (i) {\n          return cb !== i;\n        });\n      };\n    }\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvbmFuby1ldmVudHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBLDZGQUE2RixhQUFhO0FBQzFHO0FBQ0E7QUFDQSx1RkFBdUYsWUFBWTtBQUNuRztBQUNBO0FBQ0EsS0FBSztBQUNMLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRlci1lZGl0b3IvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvbmFuby1ldmVudHMuanM/ZmNlNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdmFyIGNyZWF0ZU5hbm9FdmVudHMgPSBmdW5jdGlvbiBjcmVhdGVOYW5vRXZlbnRzKCkge1xuICByZXR1cm4ge1xuICAgIGVtaXQ6IGZ1bmN0aW9uIGVtaXQoZXZlbnQpIHtcbiAgICAgIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBhcmdzID0gbmV3IEFycmF5KF9sZW4gPiAxID8gX2xlbiAtIDEgOiAwKSwgX2tleSA9IDE7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgICAgYXJnc1tfa2V5IC0gMV0gPSBhcmd1bWVudHNbX2tleV07XG4gICAgICB9XG4gICAgICBmb3IgKHZhciBpID0gMCwgY2FsbGJhY2tzID0gdGhpcy5ldmVudHNbZXZlbnRdIHx8IFtdLCBsZW5ndGggPSBjYWxsYmFja3MubGVuZ3RoOyBpIDwgbGVuZ3RoOyBpKyspIHtcbiAgICAgICAgY2FsbGJhY2tzW2ldLmFwcGx5KGNhbGxiYWNrcywgYXJncyk7XG4gICAgICB9XG4gICAgfSxcbiAgICBldmVudHM6IHt9LFxuICAgIG9uOiBmdW5jdGlvbiBvbihldmVudCwgY2IpIHtcbiAgICAgIHZhciBfdGhpcyRldmVudHMsXG4gICAgICAgIF90aGlzID0gdGhpcztcbiAgICAgICgoX3RoaXMkZXZlbnRzID0gdGhpcy5ldmVudHMpW2V2ZW50XSB8fCAoX3RoaXMkZXZlbnRzW2V2ZW50XSA9IFtdKSkucHVzaChjYik7XG4gICAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICB2YXIgX3RoaXMkZXZlbnRzJGV2ZW50O1xuICAgICAgICBfdGhpcy5ldmVudHNbZXZlbnRdID0gKF90aGlzJGV2ZW50cyRldmVudCA9IF90aGlzLmV2ZW50c1tldmVudF0pID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRldmVudHMkZXZlbnQuZmlsdGVyKGZ1bmN0aW9uIChpKSB7XG4gICAgICAgICAgcmV0dXJuIGNiICE9PSBpO1xuICAgICAgICB9KTtcbiAgICAgIH07XG4gICAgfVxuICB9O1xufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/nano-events.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/scope.js":
/*!****************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/scope.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusScope: () => (/* binding */ focusScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nvar focusScope = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvc2NvcGUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXNDO0FBQy9CLDhCQUE4QixvREFBYSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1aWxkZXItZWRpdG9yLy4uLy4uL25vZGVfbW9kdWxlcy9yZWFjdC1mb2N1cy1sb2NrL2Rpc3QvZXMyMDE1L3Njb3BlLmpzP2YyMTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlQ29udGV4dCB9IGZyb20gJ3JlYWN0JztcbmV4cG9ydCB2YXIgZm9jdXNTY29wZSA9IC8qI19fUFVSRV9fKi9jcmVhdGVDb250ZXh0KHVuZGVmaW5lZCk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/scope.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/use-focus-scope.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/use-focus-scope.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFocusController: () => (/* binding */ useFocusController),\n/* harmony export */   useFocusScope: () => (/* binding */ useFocusScope)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/../../node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _scope__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./scope */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/scope.js\");\n/* harmony import */ var _medium__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./medium */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/medium.js\");\n/* harmony import */ var _util__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./util */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/util.js\");\n\n\n\n\n\nvar collapseRefs = function collapseRefs(shards) {\n  return shards.map(_util__WEBPACK_IMPORTED_MODULE_2__.extractRef).filter(Boolean);\n};\nvar withMedium = function withMedium(fn) {\n  return new Promise(function (resolve) {\n    return _medium__WEBPACK_IMPORTED_MODULE_3__.mediumEffect.useMedium(function () {\n      resolve(fn.apply(void 0, arguments));\n    });\n  });\n};\nvar useFocusController = function useFocusController() {\n  for (var _len = arguments.length, shards = new Array(_len), _key = 0; _key < _len; _key++) {\n    shards[_key] = arguments[_key];\n  }\n  if (!shards.length) {\n    throw new Error('useFocusController requires at least one target element');\n  }\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(shards);\n  ref.current = shards;\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {\n    return {\n      autoFocus: function autoFocus(focusOptions) {\n        if (focusOptions === void 0) {\n          focusOptions = {};\n        }\n        return withMedium(function (car) {\n          return car.moveFocusInside(collapseRefs(ref.current), null, focusOptions);\n        });\n      },\n      focusNext: function focusNext(options) {\n        return withMedium(function (car) {\n          car.moveFocusInside(collapseRefs(ref.current), null);\n          car.focusNextElement(document.activeElement, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            scope: collapseRefs(ref.current)\n          }, options));\n        });\n      },\n      focusPrev: function focusPrev(options) {\n        return withMedium(function (car) {\n          car.moveFocusInside(collapseRefs(ref.current), null);\n          car.focusPrevElement(document.activeElement, (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n            scope: collapseRefs(ref.current)\n          }, options));\n        });\n      },\n      focusFirst: function focusFirst(options) {\n        return withMedium(function (car) {\n          car.focusFirstElement(collapseRefs(ref.current), options);\n        });\n      },\n      focusLast: function focusLast(options) {\n        return withMedium(function (car) {\n          car.focusLastElement(collapseRefs(ref.current), options);\n        });\n      }\n    };\n  }, []);\n};\nvar useFocusScope = function useFocusScope() {\n  var scope = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_scope__WEBPACK_IMPORTED_MODULE_4__.focusScope);\n  if (!scope) {\n    throw new Error('FocusLock is required to operate with FocusScope');\n  }\n  return useFocusController.apply(void 0, [scope.observed].concat(scope.shards));\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/use-focus-scope.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/use-focus-state.js":
/*!**************************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/use-focus-state.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFocusState: () => (/* binding */ useFocusState)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _nano_events__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./nano-events */ \"(ssr)/../../node_modules/react-focus-lock/dist/es2015/nano-events.js\");\n\n\nvar mainbus = (0,_nano_events__WEBPACK_IMPORTED_MODULE_1__.createNanoEvents)();\nvar subscribeCounter = 0;\nvar onFocusIn = function onFocusIn(event) {\n  return mainbus.emit('assign', event.target);\n};\nvar onFocusOut = function onFocusOut(event) {\n  return mainbus.emit('reset', event.target);\n};\nvar useDocumentFocusSubscribe = function useDocumentFocusSubscribe() {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (!subscribeCounter) {\n      document.addEventListener('focusin', onFocusIn);\n      document.addEventListener('focusout', onFocusOut);\n    }\n    subscribeCounter += 1;\n    return function () {\n      subscribeCounter -= 1;\n      if (!subscribeCounter) {\n        document.removeEventListener('focusin', onFocusIn);\n        document.removeEventListener('focusout', onFocusOut);\n      }\n    };\n  }, []);\n};\nvar getFocusState = function getFocusState(target, current) {\n  if (target === current) {\n    return 'self';\n  }\n  if (current.contains(target)) {\n    return 'within';\n  }\n  return 'within-boundary';\n};\nvar useFocusState = function useFocusState(callbacks) {\n  if (callbacks === void 0) {\n    callbacks = {};\n  }\n  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),\n    active = _useState[0],\n    setActive = _useState[1];\n  var _useState2 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(''),\n    state = _useState2[0],\n    setState = _useState2[1];\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  var focusState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n  var stateTracker = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (ref.current) {\n      var isAlreadyFocused = ref.current === document.activeElement || ref.current.contains(document.activeElement);\n      setActive(isAlreadyFocused);\n      setState(getFocusState(document.activeElement, ref.current));\n      if (isAlreadyFocused && callbacks.onFocus) {\n        callbacks.onFocus();\n      }\n    }\n  }, []);\n  var onFocus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (e) {\n    focusState.current = {\n      focused: true,\n      state: getFocusState(e.target, e.currentTarget)\n    };\n  }, []);\n  useDocumentFocusSubscribe();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    var fout = mainbus.on('reset', function () {\n      focusState.current = {};\n    });\n    var fin = mainbus.on('assign', function () {\n      var newState = focusState.current.focused || false;\n      setActive(newState);\n      setState(focusState.current.state || '');\n      if (newState !== stateTracker.current) {\n        stateTracker.current = newState;\n        if (newState) {\n          callbacks.onFocus && callbacks.onFocus();\n        } else {\n          callbacks.onBlur && callbacks.onBlur();\n        }\n      }\n    });\n    return function () {\n      fout();\n      fin();\n    };\n  }, []);\n  return {\n    active: active,\n    state: state,\n    onFocus: onFocus,\n    ref: ref\n  };\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/use-focus-state.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/react-focus-lock/dist/es2015/util.js":
/*!***************************************************************!*\
  !*** ../../node_modules/react-focus-lock/dist/es2015/util.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deferAction: () => (/* binding */ deferAction),\n/* harmony export */   extractRef: () => (/* binding */ extractRef),\n/* harmony export */   inlineProp: () => (/* binding */ inlineProp)\n/* harmony export */ });\nfunction deferAction(action) {\n  setTimeout(action, 1);\n}\nvar inlineProp = function inlineProp(name, value) {\n  var obj = {};\n  obj[name] = value;\n  return obj;\n};\nvar extractRef = function extractRef(ref) {\n  return ref && 'current' in ref ? ref.current : ref;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVpbGRlci1lZGl0b3IvLi4vLi4vbm9kZV9tb2R1bGVzL3JlYWN0LWZvY3VzLWxvY2svZGlzdC9lczIwMTUvdXRpbC5qcz9jMDBlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBmdW5jdGlvbiBkZWZlckFjdGlvbihhY3Rpb24pIHtcbiAgc2V0VGltZW91dChhY3Rpb24sIDEpO1xufVxuZXhwb3J0IHZhciBpbmxpbmVQcm9wID0gZnVuY3Rpb24gaW5saW5lUHJvcChuYW1lLCB2YWx1ZSkge1xuICB2YXIgb2JqID0ge307XG4gIG9ialtuYW1lXSA9IHZhbHVlO1xuICByZXR1cm4gb2JqO1xufTtcbmV4cG9ydCB2YXIgZXh0cmFjdFJlZiA9IGZ1bmN0aW9uIGV4dHJhY3RSZWYocmVmKSB7XG4gIHJldHVybiByZWYgJiYgJ2N1cnJlbnQnIGluIHJlZiA/IHJlZi5jdXJyZW50IDogcmVmO1xufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/react-focus-lock/dist/es2015/util.js\n");

/***/ })

};
;