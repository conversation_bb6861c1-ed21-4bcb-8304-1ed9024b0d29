"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/demo/page",{

/***/ "(app-pages-browser)/./lib/stores/editorStore.ts":
/*!***********************************!*\
  !*** ./lib/stores/editorStore.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEditorStore: function() { return /* binding */ useEditorStore; }\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/../../node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/../../node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useEditorStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.devtools)((set, get)=>({\n        // Initial state\n        currentPage: null,\n        selectedElement: null,\n        selectedSection: null,\n        selectedElements: [],\n        clipboard: {\n            elements: [],\n            sections: []\n        },\n        isPreviewMode: false,\n        currentBreakpoint: \"desktop\",\n        isDragging: false,\n        editMode: \"visual\",\n        undoStack: [],\n        redoStack: [],\n        // Basic setters\n        setCurrentPage: (page)=>set({\n                currentPage: page\n            }),\n        selectElement: (element)=>set({\n                selectedElement: element,\n                selectedElements: element ? [\n                    element\n                ] : []\n            }),\n        selectSection: (section)=>set({\n                selectedSection: section\n            }),\n        // Multi-selection actions\n        addToSelection: (element)=>{\n            const state = get();\n            if (!state.selectedElements.find((e)=>e.id === element.id)) {\n                set({\n                    selectedElements: [\n                        ...state.selectedElements,\n                        element\n                    ],\n                    selectedElement: element\n                });\n            }\n        },\n        removeFromSelection: (elementId)=>{\n            const state = get();\n            const newSelection = state.selectedElements.filter((e)=>e.id !== elementId);\n            set({\n                selectedElements: newSelection,\n                selectedElement: newSelection.length > 0 ? newSelection[newSelection.length - 1] : null\n            });\n        },\n        clearSelection: ()=>set({\n                selectedElements: [],\n                selectedElement: null,\n                selectedSection: null\n            }),\n        selectMultiple: (elements)=>set({\n                selectedElements: elements,\n                selectedElement: elements.length > 0 ? elements[elements.length - 1] : null\n            }),\n        // Clipboard operations\n        copyToClipboard: function(elements) {\n            let sections = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : [];\n            set({\n                clipboard: {\n                    elements: elements.map((el)=>({\n                            ...el,\n                            id: \"\".concat(el.id, \"_copy\")\n                        })),\n                    sections: sections.map((sec)=>({\n                            ...sec,\n                            id: \"\".concat(sec.id, \"_copy\")\n                        }))\n                }\n            });\n        },\n        pasteFromClipboard: (targetSectionId)=>{\n            const state = get();\n            if (!state.currentPage || !targetSectionId) return;\n            const { elements } = state.clipboard;\n            if (elements.length === 0) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const targetSection = newPage.sections.find((s)=>s.id === targetSectionId);\n            if (targetSection) {\n                elements.forEach((element)=>{\n                    const newElement = {\n                        ...element,\n                        id: \"element_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9))\n                    };\n                    targetSection.elements.push(newElement);\n                });\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"add\",\n                    target: \"element\",\n                    data: {\n                        elements,\n                        targetSectionId\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        // Element operations\n        addElement: (element, sectionId)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const section = newPage.sections.find((s)=>s.id === sectionId);\n            if (section) {\n                section.elements.push(element);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"add\",\n                    target: \"element\",\n                    data: {\n                        element,\n                        sectionId\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        updateElement: (id, props)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const element = get().getElementById(id);\n            if (element) {\n                Object.assign(element, props);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"update\",\n                    target: \"element\",\n                    data: {\n                        id,\n                        props\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        deleteElement: (id)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            for (const section of newPage.sections){\n                const index = section.elements.findIndex((e)=>e.id === id);\n                if (index !== -1) {\n                    var _state_selectedElement;\n                    const deletedElement = section.elements.splice(index, 1)[0];\n                    set({\n                        currentPage: newPage,\n                        selectedElement: ((_state_selectedElement = state.selectedElement) === null || _state_selectedElement === void 0 ? void 0 : _state_selectedElement.id) === id ? null : state.selectedElement\n                    });\n                    // Add to history\n                    get().addToHistory({\n                        type: \"delete\",\n                        target: \"element\",\n                        data: {\n                            element: deletedElement,\n                            sectionId: section.id\n                        },\n                        timestamp: Date.now()\n                    });\n                    break;\n                }\n            }\n        },\n        moveElement: (elementId, newParentId, index)=>{\n            // Implementation for moving elements between sections\n            const state = get();\n            if (!state.currentPage) return;\n            // Add to history\n            get().addToHistory({\n                type: \"move\",\n                target: \"element\",\n                data: {\n                    elementId,\n                    newParentId,\n                    index\n                },\n                timestamp: Date.now()\n            });\n        },\n        // Section operations\n        addSection: (section, index)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            if (index !== undefined) {\n                newPage.sections.splice(index, 0, section);\n            } else {\n                newPage.sections.push(section);\n            }\n            set({\n                currentPage: newPage\n            });\n            // Add to history\n            get().addToHistory({\n                type: \"add\",\n                target: \"section\",\n                data: {\n                    section,\n                    index\n                },\n                timestamp: Date.now()\n            });\n        },\n        updateSection: (id, props)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const section = newPage.sections.find((s)=>s.id === id);\n            if (section) {\n                Object.assign(section, props);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"update\",\n                    target: \"section\",\n                    data: {\n                        id,\n                        props\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        deleteSection: (id)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const index = newPage.sections.findIndex((s)=>s.id === id);\n            if (index !== -1) {\n                var _state_selectedSection;\n                const deletedSection = newPage.sections.splice(index, 1)[0];\n                set({\n                    currentPage: newPage,\n                    selectedSection: ((_state_selectedSection = state.selectedSection) === null || _state_selectedSection === void 0 ? void 0 : _state_selectedSection.id) === id ? null : state.selectedSection\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"delete\",\n                    target: \"section\",\n                    data: {\n                        section: deletedSection,\n                        index\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        moveSection: (sectionId, newIndex)=>{\n            const state = get();\n            if (!state.currentPage) return;\n            const newPage = {\n                ...state.currentPage\n            };\n            const currentIndex = newPage.sections.findIndex((s)=>s.id === sectionId);\n            if (currentIndex !== -1) {\n                const section = newPage.sections.splice(currentIndex, 1)[0];\n                newPage.sections.splice(newIndex, 0, section);\n                set({\n                    currentPage: newPage\n                });\n                // Add to history\n                get().addToHistory({\n                    type: \"move\",\n                    target: \"section\",\n                    data: {\n                        sectionId,\n                        currentIndex,\n                        newIndex\n                    },\n                    timestamp: Date.now()\n                });\n            }\n        },\n        // Editor controls\n        setPreviewMode: (isPreview)=>set({\n                isPreviewMode: isPreview\n            }),\n        setBreakpoint: (breakpoint)=>set({\n                currentBreakpoint: breakpoint\n            }),\n        setDragging: (isDragging)=>set({\n                isDragging\n            }),\n        // History operations\n        undo: ()=>{\n            const state = get();\n            if (state.undoStack.length === 0) return;\n            const action = state.undoStack[state.undoStack.length - 1];\n            const newUndoStack = state.undoStack.slice(0, -1);\n            const newRedoStack = [\n                ...state.redoStack,\n                action\n            ];\n            // Reverse the action\n            // Implementation depends on action type\n            set({\n                undoStack: newUndoStack,\n                redoStack: newRedoStack\n            });\n        },\n        redo: ()=>{\n            const state = get();\n            if (state.redoStack.length === 0) return;\n            const action = state.redoStack[state.redoStack.length - 1];\n            const newRedoStack = state.redoStack.slice(0, -1);\n            const newUndoStack = [\n                ...state.undoStack,\n                action\n            ];\n            // Reapply the action\n            // Implementation depends on action type\n            set({\n                undoStack: newUndoStack,\n                redoStack: newRedoStack\n            });\n        },\n        addToHistory: (action)=>{\n            const state = get();\n            const newUndoStack = [\n                ...state.undoStack,\n                action\n            ];\n            // Limit history size\n            if (newUndoStack.length > 50) {\n                newUndoStack.shift();\n            }\n            set({\n                undoStack: newUndoStack,\n                redoStack: [] // Clear redo stack when new action is added\n            });\n        },\n        // Utility functions\n        getElementById: (id)=>{\n            const state = get();\n            if (!state.currentPage) return null;\n            for (const section of state.currentPage.sections){\n                const element = section.elements.find((e)=>e.id === id);\n                if (element) return element;\n            }\n            return null;\n        },\n        getSectionById: (id)=>{\n            const state = get();\n            if (!state.currentPage) return null;\n            return state.currentPage.sections.find((s)=>s.id === id) || null;\n        }\n    }), {\n    name: \"editor-store\"\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/stores/editorStore.ts\n"));

/***/ })

});